[tool.poetry]
name = "ragchecker"
version = "0.1.9"
description = "RAGChecker: A Fine-grained Framework For Diagnosing Retrieval-Augmented Generation (RAG) systems."
authors = [
    "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
]
readme = "README.md"
license = "Apache-2.0"

[tool.poetry.dependencies]
python = "^3.9"
refchecker = "^0.2"
loguru = "^0.7"
dataclasses-json = "^0.6"


[tool.poetry.scripts]
ragchecker-cli = "ragchecker.cli:main"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
