name: Publish to PyPI
on:
  release:
    types: [published]

jobs:
  pypi_release:
    name: Builds Using Poetry and Publishes to PyPI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - name: Install poetry
        uses: abatilo/actions-poetry@v2
      - run: poetry config pypi-token.pypi "${{ secrets.PYPI_API_TOKEN }}"
      - name: Publish package
        run: poetry publish --build