{"input_data": [{"query_id": "20", "query": "Why doesn't <PERSON><PERSON><PERSON><PERSON><PERSON> want to meet his landlady?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> doesn't want to meet his landlady because he owes her money and is hopelessly in debt to her, which makes him afraid of encountering her.", "novel": "Crime And Punishment"}, {"query_id": "21", "query": "What is <PERSON><PERSON><PERSON><PERSON><PERSON>'s ulterior motive for visiting the old widow's house?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>'s ulterior motive for visiting the old widow's house is to rehearse the murder. He was positively going for a rehearsal of his project, and at every step, his excitement grew more and more violent.", "novel": "Crime And Punishment"}, {"query_id": "22", "query": "How does <PERSON><PERSON><PERSON><PERSON><PERSON> feel when he leaves the old widow's house?", "gt_answer": "He is disgusted with himself, feeling that his heart is capable of filthy, loathsome things. He is appalled that such an atrocious thought could even enter his mind and is deeply disturbed by the realization of his own capacity for such thoughts.", "novel": "Crime And Punishment"}, {"query_id": "23", "query": "Whom does <PERSON><PERSON><PERSON><PERSON><PERSON> meet in the tavern?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> meets <PERSON><PERSON><PERSON><PERSON> in the tavern. <PERSON><PERSON><PERSON><PERSON> introduces himself as a titular counsellor and engages <PERSON><PERSON><PERSON><PERSON><PERSON> in conversation, noting that he respects education and genuine sentiments.", "novel": "Crime And Punishment"}, {"query_id": "24", "query": "Why does <PERSON><PERSON><PERSON> want to marry <PERSON><PERSON><PERSON>?", "gt_answer": "<PERSON><PERSON><PERSON> wants to marry <PERSON><PERSON><PERSON> because he believes that a man should not be indebted to his wife and that it is better for a wife to view her husband as her benefactor. He had decided to marry a girl of good reputation, without a dowry, and one who had experienced poverty, which aligns with his desire for <PERSON><PERSON><PERSON> to always look upon him as her benefactor.", "novel": "Crime And Punishment"}, {"query_id": "25", "query": "What does <PERSON><PERSON><PERSON><PERSON><PERSON> decide on the walk he takes after reading his mother's letter?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> decides that he will not let his sister marry <PERSON><PERSON><PERSON>. His mother's letter had been a torture to him, but regarding the main issue, he felt no hesitation even while reading it. The essential question was settled irrevocably in his mind: he would never allow such a marriage while he was alive, and he damned Mr. <PERSON><PERSON>.", "novel": "Crime And Punishment"}, {"query_id": "26", "query": "What did the student in the bar say that coincided with <PERSON><PERSON><PERSON><PERSON><PERSON>'s own thoughts?", "gt_answer": "The student in the bar expressed the idea that humanity would be better off if <PERSON><PERSON><PERSON> were dead, suggesting that killing her and taking her money could be justified if the money were then used to serve humanity and the greater good.", "novel": "Crime And Punishment"}, {"query_id": "27", "query": "Who else does <PERSON><PERSON><PERSON><PERSON><PERSON> kill in <PERSON><PERSON><PERSON>'s apartment?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> also kills <PERSON><PERSON><PERSON> in <PERSON><PERSON><PERSON>'s apartment. <PERSON><PERSON><PERSON>, who was simple and thoroughly crushed and scared, did not even raise a hand to guard her face when the axe was raised over her.", "novel": "Crime And Punishment"}, {"query_id": "28", "query": "Why does <PERSON><PERSON><PERSON><PERSON><PERSON> think he's being punished for the murder already?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> thinks he's being punished for the murder already because he has a hard time concentrating, and he feels that all his faculties, including memory and the simplest power of reflection, are failing him, which has become an insufferable torture. He is convinced that his punishment is already beginning.", "novel": "Crime And Punishment"}, {"query_id": "29", "query": "Why is <PERSON><PERSON><PERSON><PERSON><PERSON> called to the police station?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> is called to the police station to answer charges that he owes his landlady money. Specifically, he is summoned for the recovery of money on an IOU, which amounts to a hundred and fifteen roubles. This IOU was legally attested and due for payment, having been given by <PERSON><PERSON><PERSON><PERSON><PERSON> to the widow of the assessor <PERSON><PERSON><PERSON><PERSON><PERSON> nine months ago and subsequently paid over to Mr<PERSON><PERSON>. The police require him to either pay the debt with all associated costs or provide a written declaration of when he can pay it, along with an undertaking not to leave the capital or conceal his property until the debt is settled.", "novel": "Crime And Punishment"}, {"query_id": "30", "query": "What does <PERSON><PERSON><PERSON><PERSON><PERSON> bury in the courtyard?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> buried his tracks by turning over a stone in the courtyard, creating a small hollow in the ground where he emptied his pocket, placing the purse at the top. He then turned the stone back to its original position, ensuring it looked undisturbed by scraping the earth around it and pressing the edges with his foot.", "novel": "Crime And Punishment"}, {"query_id": "31", "query": "Why is <PERSON><PERSON><PERSON><PERSON> lying in the street?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON> is lying in the street because he is drunk and has been run over by a horse, having thrown himself under the horses in his intoxicated state.", "novel": "Crime And Punishment"}, {"query_id": "32", "query": "What does <PERSON><PERSON><PERSON><PERSON><PERSON> order <PERSON><PERSON><PERSON> to do?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> orders <PERSON><PERSON><PERSON> to end her engagement with <PERSON><PERSON><PERSON>, stating that he does not want the marriage to happen and instructing her to refuse <PERSON><PERSON><PERSON> at the first opportunity the next day, ensuring that they never hear his name again.", "novel": "Crime And Punishment"}, {"query_id": "33", "query": "Who interrupts the conversation between <PERSON><PERSON><PERSON><PERSON><PERSON>, his family, and <PERSON><PERSON><PERSON><PERSON><PERSON>?", "gt_answer": "<PERSON> interrupts the conversation between <PERSON><PERSON><PERSON><PERSON><PERSON>, his family, and <PERSON><PERSON><PERSON><PERSON><PERSON>. She arrives falteringly, apologizing for disturbing them and explaining that she has come on behalf of <PERSON><PERSON>, who had no one else to send. <PERSON> conveys <PERSON><PERSON>'s request for them to attend the service in the morning at Mitrofanievsky and then to visit her.", "novel": "Crime And Punishment"}, {"query_id": "34", "query": "What does <PERSON><PERSON><PERSON><PERSON> ask <PERSON><PERSON><PERSON><PERSON><PERSON> about in an attempt to trap him?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON> asks <PERSON><PERSON><PERSON><PERSON><PERSON> about the painters near <PERSON><PERSON><PERSON>'s apartment in an attempt to trap him. He inquires whether <PERSON><PERSON><PERSON><PERSON><PERSON> had seen the painters on the day of the murder, hoping to catch him in a lie or inconsistency. <PERSON><PERSON><PERSON><PERSON><PERSON>, sensing the trap, denies seeing any painters and tries to recall details about the flat opposite <PERSON><PERSON><PERSON>'s, mentioning porters moving a sofa. <PERSON><PERSON><PERSON><PERSON> then points out that the painters were working on the day of the murder, while <PERSON><PERSON><PERSON><PERSON><PERSON> was there three days before, leading <PERSON><PERSON><PERSON><PERSON> to realize his mistake and apologize for the confusion.", "novel": "Crime And Punishment"}, {"query_id": "35", "query": "What does the stranger in the street say to <PERSON><PERSON><PERSON><PERSON><PERSON>?", "gt_answer": "The stranger in the street suddenly says \"Murderer!\" in a quiet but clear and distinct voice. <PERSON><PERSON><PERSON><PERSON><PERSON> continues walking beside him, feeling his legs suddenly weaken, a cold shiver running down his spine, and his heart seeming to stand still for a moment before suddenly beginning to throb as though it were set free. They walk for about a hundred paces side by side in silence.", "novel": "Crime And Punishment"}, {"query_id": "36", "query": "Who does <PERSON> believe will take care of her family?", "gt_answer": "<PERSON> believes that <PERSON> will take care of her family. She is in a state of desperation and repeatedly expresses her faith that <PERSON> would not allow anything terrible to happen to them. Despite the dire situation, <PERSON> clings to the hope and conviction that <PERSON> will protect her family from harm.", "novel": "Crime And Punishment"}, {"query_id": "37", "query": "What has <PERSON><PERSON><PERSON><PERSON><PERSON> resolved to do?", "gt_answer": "<PERSON><PERSON><PERSON><PERSON><PERSON> has resolved to separate himself from his family, as he has abandoned his mother and sister and decided not to see them anymore, breaking all ties with them completely.", "novel": "Crime And Punishment"}, {"query_id": "38", "query": "Who eavesdrops on <PERSON><PERSON><PERSON><PERSON><PERSON>'s conversation with <PERSON>?", "gt_answer": "Mr. <PERSON><PERSON><PERSON><PERSON><PERSON> eavesdrops on <PERSON><PERSON><PERSON><PERSON><PERSON>'s conversation with <PERSON>. He stands listening at the door of the empty room adjacent to <PERSON>'s room, which had long been uninhabited.", "novel": "Crime And Punishment"}, {"query_id": "39", "query": "What does <PERSON><PERSON><PERSON> believe might have kept <PERSON><PERSON><PERSON> from breaking their engagement?", "gt_answer": "<PERSON><PERSON><PERSON> believes that if he had spent around fifteen hundred roubles on trousseau and presents, including knick-knacks, dressing-cases, jewelry, materials, and other items from <PERSON><PERSON><PERSON>'s and the English shop, his position would have been better and stronger, making it more difficult for <PERSON><PERSON><PERSON> to refuse him so easily.", "novel": "Crime And Punishment"}, {"query_id": "40", "query": "After <PERSON> requests more gruel, what do the workhouse authorities offer to whomever takes him off their hands?", "gt_answer": "After <PERSON> requests more gruel, the workhouse authorities offer a reward of five pounds to anybody who would take <PERSON> off the hands of the parish. In other words, five pounds and <PERSON> were offered to any man or woman who wanted an apprentice to any trade, business, or calling.", "novel": "<PERSON>"}, {"query_id": "41", "query": "What is the profession of Mr<PERSON> <PERSON>, who offers to take <PERSON> on as an apprentice?", "gt_answer": "Mr. <PERSON><PERSON><PERSON> is a chimney-sweep who, while walking down the High Street one morning, was deeply thinking about how to pay his overdue rent, as his landlord had become quite insistent.", "novel": "<PERSON>"}, {"query_id": "42", "query": "After feeding <PERSON> his first meal at her home, what is Mrs. <PERSON><PERSON><PERSON>'s concern about <PERSON>?", "gt_answer": "Mrs. <PERSON><PERSON><PERSON> is concerned about <PERSON>'s large appetite after feeding him his first meal at her home. She remarks that although <PERSON> is a very good-looking boy, he eats a lot, indicating her worry about the amount of food he consumes.", "novel": "<PERSON>"}, {"query_id": "43", "query": "At the first burial <PERSON> attends as Mr. <PERSON><PERSON><PERSON>'s apprentice, what is the deceased's cause of death?", "gt_answer": "The deceased's cause of death at the first burial <PERSON> attends as Mr. <PERSON><PERSON><PERSON>'s apprentice is starvation. When the person who found her returned, she was already dying, and it was clear that she had been starved to death. The person who discovered her swore before <PERSON> that she had been starved.", "novel": "<PERSON>"}, {"query_id": "44", "query": "<PERSON> gains extensive experience in undertaking after many people die in an epidemic of what?", "gt_answer": "<PERSON> gains extensive experience in undertaking after many people die in an epidemic of measles. The oldest inhabitants could not recall a time when measles had been so widespread or so deadly to infants, leading to numerous mournful processions that little <PERSON> led, wearing a hat-band that reached down to his knees, much to the admiration and emotion of all the mothers in the town.", "novel": "<PERSON>"}, {"query_id": "45", "query": "After <PERSON> flees the <PERSON><PERSON><PERSON>'s home, he decides to walk to London, a distance of what?", "gt_answer": "<PERSON> decides to walk to London, a distance of seventy miles, after fleeing the <PERSON><PERSON><PERSON>'s home.", "novel": "<PERSON>"}, {"query_id": "46", "query": "On <PERSON>'s first morning at <PERSON><PERSON><PERSON>'s house, what weapon does <PERSON><PERSON><PERSON> briefly threaten him with?", "gt_answer": "On <PERSON>'s first morning at <PERSON><PERSON><PERSON>'s house, <PERSON><PERSON><PERSON> briefly threatens him with a bread knife, which he grabs from the table and holds up furiously, trembling so much that the knife quivers in the air.", "novel": "<PERSON>"}, {"query_id": "47", "query": "How many months of hard labor is <PERSON> sentenced to for picking Mr<PERSON>'s pocket?", "gt_answer": "<PERSON> is sentenced to three months of hard labor for picking Mr<PERSON>'s pocket.", "novel": "<PERSON>"}, {"query_id": "48", "query": "When <PERSON> awakes from his fever at the <PERSON><PERSON>'s house, who is sitting beside him?", "gt_answer": "An old lady is sitting beside <PERSON> when he awakes from his fever at the <PERSON><PERSON>'s house, softly telling him to be very quiet to avoid getting ill again and mentioning that he has been very bad, almost as bad as could be. She gently encourages him to lie down again.", "novel": "<PERSON>"}, {"query_id": "49", "query": "After <PERSON>'s arrest and trial, which of <PERSON><PERSON><PERSON>'s cohorts goes to the jail to ask after him?", "gt_answer": "<PERSON> goes to the jail to ask after <PERSON>. She enters by the back way, taps softly with the key at one of the cell doors, and listens. Hearing no sound within, she coughs and listens again. Still receiving no reply, she speaks gently, calling out, \"Nolly, dear?\" and \"Nolly?\"", "novel": "<PERSON>"}, {"query_id": "50", "query": "Who answers Mr<PERSON>'s ad in the paper that offers a reward for information on <PERSON>?", "gt_answer": "Mr. <PERSON><PERSON> answers Mr. <PERSON><PERSON>'s ad in the paper that offers a reward for information on <PERSON>. After reading the advertisement multiple times, Mr. <PERSON><PERSON> becomes so excited that he immediately heads to Pentonville, even leaving his glass of hot gin-and-water untouched. Upon arrival, he inquires if Mr. <PERSON><PERSON> is at home.", "novel": "<PERSON>"}, {"query_id": "51", "query": "At <PERSON>' house, what does <PERSON><PERSON> show to <PERSON> while threatening to kill him if he causes trouble?", "gt_answer": "<PERSON> shows <PERSON> a pistol while threatening to kill him if he causes trouble. <PERSON><PERSON> makes a menacing gesture to indicate that he has the pistol in a side-pocket of his great-coat, emphasizing the seriousness of his threat.", "novel": "<PERSON>"}, {"query_id": "52", "query": "Who admits to robbing <PERSON>'s mother on her deathbed?", "gt_answer": "<PERSON> admits to robbing <PERSON>'s mother on her deathbed. As the mother lay dying, she whispered to <PERSON>, expressing her hope that her baby, if it survived, would one day not feel ashamed of its mother's name. In her final moments, she prayed for her child to find friends and receive pity in a harsh world. <PERSON>, in her confession, reveals that she stole gold from the dying woman.", "novel": "<PERSON>"}, {"query_id": "53", "query": "Who opens the door of the mansion where the botched robbery occurred to find <PERSON>, gravely wounded, on the doorstep?", "gt_answer": "<PERSON><PERSON><PERSON>, who had got behind the door to open it, no sooner saw <PERSON>, gravely wounded, than he uttered a loud cry. Mr. <PERSON> then seized <PERSON> by one leg and one arm (fortunately not the broken limb), lugged him straight into the hall, and deposited him at full length on the floor.", "novel": "<PERSON>"}, {"query_id": "54", "query": "Who is Mr. <PERSON>?", "gt_answer": "Mr. <PERSON><PERSON> is a local surgeon known throughout a ten-mile radius as 'the doctor.' He is an eccentric old bachelor who has grown fat more from good humor than from good living. He is also known for being kind and hearty.", "novel": "<PERSON>"}, {"query_id": "55", "query": "Where do Mr. <PERSON> and <PERSON> learn that <PERSON><PERSON> and Mrs. <PERSON><PERSON> have moved to, to <PERSON>'s distress?", "gt_answer": "Mr. <PERSON><PERSON><PERSON> and <PERSON> learn that <PERSON><PERSON> and Mrs. <PERSON><PERSON> have moved to the West Indies, which causes <PERSON> great distress. <PERSON> is informed that Mr. <PERSON><PERSON> had sold off his goods and left for the West Indies six weeks prior, leading him to clasp his hands and sink feebly backward in despair.", "novel": "<PERSON>"}, {"query_id": "56", "query": "When <PERSON> goes to speak to <PERSON>, she reveals that <PERSON><PERSON> is <PERSON>'s what?", "gt_answer": "<PERSON><PERSON> is revealed to be <PERSON>'s brother when <PERSON> speaks to <PERSON>. <PERSON> explains that <PERSON><PERSON> harbors a deep hatred for <PERSON> and would go to great lengths to harm him. <PERSON><PERSON> even admits that he would take <PERSON>'s life if he could do so without endangering himself. He plans to be a constant threat to <PERSON>, using <PERSON>'s birth and history against him. <PERSON> is shocked to learn that <PERSON><PERSON> is <PERSON>'s brother.", "novel": "<PERSON>"}, {"query_id": "57", "query": "Who is revealed to be <PERSON>'s biological father?", "gt_answer": "Mr. <PERSON><PERSON>, whose full name is <PERSON>, is revealed to be <PERSON>'s biological father. <PERSON> had a relationship with <PERSON>, who tragically died while giving birth to <PERSON>.", "novel": "<PERSON>"}, {"query_id": "58", "query": "What occupation does <PERSON> choose to pursue, leading <PERSON> to agree to marry him?", "gt_answer": "<PERSON> chooses to pursue the occupation of a clergyman, which leads <PERSON> to agree to marry him. Before three months had passed, <PERSON> and <PERSON> were married in the village church, which was henceforth to be the scene of the young clergyman's labours. On the same day, they entered into possession of their new and happy home.", "novel": "<PERSON>"}, {"query_id": "59", "query": "Which member of <PERSON><PERSON><PERSON>'s gang testifies against him and is pardoned as a result?", "gt_answer": "<PERSON>, a member of <PERSON><PERSON><PERSON>'s gang, testifies against him and is pardoned as a result. After receiving a free pardon from the Crown for his cooperation, <PERSON> finds himself in need of a new means of livelihood. Considering his previous profession unsafe, he eventually decides to become an Informer, a role in which he manages to earn a respectable living.", "novel": "<PERSON>"}, {"query_id": "60", "query": "Of what ethnicity is Fagin?", "gt_answer": "<PERSON><PERSON><PERSON> is Jewish, as indicated by the reference to him as \"the Jew\" in the novel.", "novel": "<PERSON>"}, {"query_id": "61", "query": "In novel Mansfield Park, has the word or phrase vulnerable appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the word \"vulnerable\" appears once in the novel \"Mansfield Park.\"", "novel": "Mansfield Park"}, {"query_id": "62", "query": "In novel Mansfield Park, has the word or phrase hectic appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the word \"hectic\" has appeared in the novel \"Mansfield Park\" exactly once.", "novel": "Mansfield Park"}, {"query_id": "63", "query": "Has the object <PERSON><PERSON><PERSON> appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the object <PERSON><PERSON><PERSON> has appeared in the text, and it appears only once.", "novel": "Mansfield Park"}, {"query_id": "64", "query": "Has the object the Canopus appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the object the Canopus has appeared in the text, and it appears only once.", "novel": "Mansfield Park"}, {"query_id": "65", "query": "Has the place Garrison chapel appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the place Garrison chapel has appeared in the text, and it appears only once when he was asked to go with them to the Garrison chapel, which was exactly what he had intended, and they all walked there together.", "novel": "Mansfield Park"}, {"query_id": "66", "query": "Has the sentence 'Blest leaf! whose aromatic gales dispense To Templars modesty, to Parsons sense.' appeared? If so, how many times does this sentence appear in the text?", "gt_answer": "Yes, the sentence \"Blest leaf! whose aromatic gales dispense To Templars modesty, to Parsons sense.\" appears in the text, and it appears only once.", "novel": "Mansfield Park"}, {"query_id": "67", "query": "Has the character Captain <PERSON> appeared? If so, how many times does he appear in the text?", "gt_answer": "Yes, Captain <PERSON> has appeared in the text, and he appears only once.", "novel": "Mansfield Park"}, {"query_id": "68", "query": "What is the relationship between <PERSON> and <PERSON>?", "gt_answer": "Miss <PERSON> and <PERSON> are the same person. About thirty years ago, Miss <PERSON>, with a modest fortune of seven thousand pounds, managed to captivate Sir <PERSON> of Mansfield Park. This fortunate match elevated her to the status of <PERSON><PERSON>, granting her the comforts and privileges of a baronet's wife, including a handsome house and a substantial income. Lady <PERSON> is characterized by her tranquil feelings and remarkably easy and indolent temperament.", "novel": "Mansfield Park"}, {"query_id": "69", "query": "Please list 3 aliases or designations of Frances <PERSON>.", "gt_answer": "<PERSON> is known by several aliases or designations, including <PERSON>, Mrs. <PERSON>, and <PERSON>.", "novel": "Mansfield Park"}, {"query_id": "70", "query": "In novel Mansfield Park, in which chapter does there exist a sentence in the novel Mansfile Park with the same or similar meaning as '<PERSON> replies that 'if the situation need <PERSON>'s gratitude, she will show it', then <PERSON> skip the topic.'?", "gt_answer": "Chapter VIII contains a sentence where <PERSON> replies that \"<PERSON> will feel quite as grateful as the occasion requires,\" and then the subject is dropped.", "novel": "Mansfield Park"}, {"query_id": "71", "query": "In novel <PERSON>, who did not wonder that such should be his father's feelings, nor could he regret anything but the exclusion of the <PERSON><PERSON>?", "gt_answer": "<PERSON> did not wonder that such should be his father's feelings, nor could he regret anything but the exclusion of the <PERSON><PERSON>.", "novel": "Mansfield Park"}, {"query_id": "72", "query": "When he returned from which place, <PERSON> would have been glad to see Mrs. <PERSON><PERSON> no more?", "gt_answer": "When he returned from Richmond, <PERSON> would have been glad to see Mrs. <PERSON><PERSON> no more.", "novel": "Mansfield Park"}, {"query_id": "73", "query": "<PERSON> married with whom?", "gt_answer": "<PERSON> married <PERSON>.", "novel": "Mansfield Park"}, {"query_id": "74", "query": "When who came back <PERSON> had every proof that could be given in his then melancholy state of spirits, of his perfect approbation and increased regard?", "gt_answer": "When Sir <PERSON> came back, <PERSON> had every proof that could be given in his then melancholy state of spirits, of his perfect approbation and increased regard.", "novel": "Mansfield Park"}, {"query_id": "75", "query": "Who had scarcely passed the solemn-looking servants, when Lady <PERSON><PERSON> came from the drawing-room to meet her?", "gt_answer": "<PERSON> had scarcely passed the solemn-looking servants when Lady <PERSON><PERSON> came from the drawing-room to meet her.", "novel": "Mansfield Park"}, {"query_id": "76", "query": "Tom had gone from where with a party of young men to Newmarket, where a neglected fall and a good deal of drinking had brought on a fever?", "gt_answer": "<PERSON> had gone from London with a party of young men to Newmarket, where a neglected fall and a good deal of drinking had brought on a fever.", "novel": "Mansfield Park"}, {"query_id": "77", "query": "The Prices were just setting off for what the next day when Mr. <PERSON> appeared again?", "gt_answer": "The Prices were just setting off for church the next day when Mr. <PERSON> appeared again.", "novel": "Mansfield Park"}, {"query_id": "78", "query": "about how many years ago <PERSON>, of Huntingdon, with only seven thousand pounds, had the good luck to captivate Sir <PERSON>, of Mansfield Park, in the county of Northampton, and to be thereby raised to the rank of a baronet's lady, with all the comforts and consequences of an handsome house and large income?", "gt_answer": "Thirty years ago, <PERSON> of Huntingdon, with only seven thousand pounds, had the good fortune to captivate Sir <PERSON> of Mansfield Park in the county of Northampton. This fortunate match elevated her to the status of a baronet's lady, granting her all the associated comforts and privileges, including a handsome house and a substantial income.", "novel": "Mansfield Park"}, {"query_id": "79", "query": "<PERSON> was at this time how old, and though there might not be much in her first appearance to captivate, there was, at least, nothing to disgust her relations?", "gt_answer": "<PERSON> was at this time just ten years old, and though there might not be much in her first appearance to captivate, there was, at least, nothing to disgust her relations.", "novel": "Mansfield Park"}, {"query_id": "80", "query": "In novel Wuthering Heights, has the word or phrase 'growing indignation' appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the phrase \"growing indignation\" appears once in the text of the novel \"Wuthering Heights.\"", "novel": "Wuthering Heights"}, {"query_id": "81", "query": "In novel Wuthering Heights, has the object tin cullenders appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the object tin cullenders has appeared in the novel Wuthering Heights, and it appears once in the text.", "novel": "Wuthering Heights"}, {"query_id": "82", "query": "In novel Wuthering Heights, in which countries does this story take place?", "gt_answer": "The story of Wuthering Heights takes place in England, as it is set in a remote and isolated area within the country, far removed from the stir of society.", "novel": "Wuthering Heights"}, {"query_id": "83", "query": "In novel Wuthering Heights, has the sentence ' Go- ' appeared? If so, how many times does this sentence appear in the text?", "gt_answer": "Yes, the sentence 'Go-' appears once in the novel Wuthering Heights.", "novel": "Wuthering Heights"}, {"query_id": "84", "query": "How many times has <PERSON> stepped forward?", "gt_answer": "<PERSON> has stepped forward once. He stepped forward, called him by name, touched his shoulder, and when there was no response, he took the candle and looked at him.", "novel": "Wuthering Heights"}, {"query_id": "85", "query": "How many times has the Mr<PERSON> wiped his forehead?", "gt_answer": "Mr. <PERSON><PERSON> has wiped his forehead once. He paused and wiped his forehead, with his hair clinging to it, wet with perspiration. His eyes were fixed on the red embers of the fire, and his brows were raised next to the temples, which diminished the grim aspect of his countenance but imparted a peculiar look of trouble and a painful appearance of mental tension towards one absorbing subject.", "novel": "Wuthering Heights"}, {"query_id": "86", "query": "What is the relationship between Mrs. <PERSON> and <PERSON><PERSON>?", "gt_answer": "Mrs. <PERSON> is <PERSON><PERSON>'s daughter-in-law, as confirmed by <PERSON><PERSON> himself.", "novel": "Wuthering Heights"}, {"query_id": "87", "query": "Please list 3 aliases or designations of <PERSON>.", "gt_answer": "<PERSON>, <PERSON>, and <PERSON> are three aliases or designations of <PERSON>.", "novel": "Wuthering Heights"}, {"query_id": "88", "query": "Please list 3 aliases or designations of Mrs. <PERSON>.", "gt_answer": "The beneficent fairy, the little witch, the widow are three aliases or designations of Mrs. <PERSON>.", "novel": "Wuthering Heights"}, {"query_id": "89", "query": "How many times have old Mr<PERSON> and <PERSON> communicated with each other?", "gt_answer": "Old Mr<PERSON><PERSON><PERSON> and <PERSON> have communicated with each other once. I remember the master, before he fell into a doze, stroking her bonny hair—it pleased him rarely to see her gentle—and saying, \"Why canst thou not always be a good lass, <PERSON>?\" And she turned her face up to his, and laughed, and answered, \"Why cannot you always be a good man, father?\"", "novel": "Wuthering Heights"}, {"query_id": "90", "query": "How many times have <PERSON><PERSON> and <PERSON> communicated with each other?", "gt_answer": "Mrs. <PERSON><PERSON><PERSON><PERSON> and <PERSON> have communicated with each other only once.", "novel": "Wuthering Heights"}, {"query_id": "91", "query": "Explain the meaning or implication of the symbol or metaphor 'cloud' within one sentence, which appears in Chapter II of the novel Wuthering Heights.", "gt_answer": "The symbol 'cloud' in Chapter II of Wuthering Heights implies that everyone sat in a grim and taciturn manner, reflecting an austere silence during the meal.", "novel": "Wuthering Heights"}, {"query_id": "92", "query": "In novel Wuthering Heights, in which chapter does there exist a sentence with the same or similar meaning as 'He responded with a nod. '?", "gt_answer": "The sentence with the same or similar meaning as \"He responded with a nod\" exists in Chapter I of the novel Wuthering Heights.", "novel": "Wuthering Heights"}, {"query_id": "93", "query": "In novel Wuthering Heights, in which chapter does there exist a sentence with the same or similar meaning as 'She quickly stood up and balanced herself on the chair's arm. '?", "gt_answer": "Chapter XV contains a sentence with a similar meaning to \"She quickly stood up and balanced herself on the chair's arm,\" which is \"In her eagerness she rose and supported herself on the arm of the chair.\"", "novel": "Wuthering Heights"}, {"query_id": "94", "query": "In novel Wuthering Heights, who is curate?", "gt_answer": "In the novel Wuthering Heights, the curate is a person who made a living by teaching the little Lintons and Earnshaws, and farming his bit of land himself.", "novel": "Wuthering Heights"}, {"query_id": "95", "query": "Who are in the <PERSON> family?", "gt_answer": "The <PERSON><PERSON><PERSON><PERSON> family consists of <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mr. <PERSON>, and Mrs. <PERSON><PERSON><PERSON><PERSON>.", "novel": "Wuthering Heights"}, {"query_id": "96", "query": "In novel Wuthering Heights, in which chapter does there exist a sentence with the same or similar meaning as 'He had previously been lively and in good health, but his strength departed abruptly. As he became restricted to the corner by the chimney, his irritability worsened considerably.?", "gt_answer": "Chapter 5 of Wuthering Heights contains a sentence that conveys the meaning that he had previously been lively and in good health, but his strength departed abruptly. As he became restricted to the corner by the chimney, his irritability worsened considerably.", "novel": "Wuthering Heights"}, {"query_id": "97", "query": "What is the relationship between Mr. <PERSON><PERSON> and <PERSON><PERSON>?", "gt_answer": "Mr. <PERSON><PERSON><PERSON> was the father of <PERSON><PERSON>. Before I came to live here, she commenced-waiting no farther invitation to her story-I was almost always at Wuthering Heights; because my mother had nursed Mr. <PERSON><PERSON><PERSON>, that was <PERSON><PERSON>'s father, and I got used to playing with the children: I ran errands too, and helped to make hay, and hung about the farm ready for anything that anybody would set me to.", "novel": "Wuthering Heights"}, {"query_id": "98", "query": "In novel Pride and Prejudice, has the word or phrase 'the young people's engagement' appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the phrase \"the young people's engagement\" appears once in the text.", "novel": "Pride and Prejudice"}, {"query_id": "99", "query": "In novel Pride and Prejudice, has the word or phrase ' a good scheme' appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the phrase \"a good scheme\" has appeared in the novel <PERSON> and Prejudice, and it appears once in the text. <PERSON> uses the phrase when she says, \"That would be a good scheme,\" indicating her approval of a particular plan, provided that there is certainty that they would not offer to send her home.", "novel": "Pride and Prejudice"}, {"query_id": "100", "query": "Has the place Hunsford Lane appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, Hunsford Lane has appeared in the text, and it appears once. Mr. <PERSON> was walking the whole morning within view of the lodges opening into Hunsford Lane to have the earliest assurance of someone's arrival.", "novel": "Pride and Prejudice"}, {"query_id": "101", "query": "Has the sentence 'My affections and wishes are unchanged; but one word from you will silence me on this subject for ever.' appeared? If so, how many times does this sentence appear in the text?", "gt_answer": "Yes, the sentence \"My affections and wishes are unchanged; but one word from you will silence me on this subject for ever.\" has appeared in the text, and it appears only once.", "novel": "Pride and Prejudice"}, {"query_id": "102", "query": "How many times has <PERSON>  couldn't help doing something?", "gt_answer": "<PERSON> couldn't help doing something once. When the dancing recommenced and <PERSON> approached to claim her hand, <PERSON> could not help cautioning her.", "novel": "Pride and Prejudice"}, {"query_id": "103", "query": "How many times has Mrs<PERSON> been startled?", "gt_answer": "Mrs. <PERSON> has been startled once.", "novel": "Pride and Prejudice"}, {"query_id": "104", "query": "What is the relationship between Mrs<PERSON> and <PERSON>?", "gt_answer": "Mrs. <PERSON><PERSON> is <PERSON>'s mother. She often scolds <PERSON>, as seen when she reprimands her for coughing and complains that it affects her nerves.", "novel": "Pride and Prejudice"}, {"query_id": "105", "query": "What is the relationship between Mr<PERSON> and <PERSON><PERSON>?", "gt_answer": "<PERSON><PERSON> is Mr. <PERSON><PERSON>'s daughter. Observing his second daughter employed in trimming a hat, he suddenly addressed her with, \"I hope Mr. <PERSON><PERSON> will like it, <PERSON><PERSON>.\"", "novel": "Pride and Prejudice"}, {"query_id": "106", "query": "What is the relationship between <PERSON> and <PERSON><PERSON>?", "gt_answer": "<PERSON> and <PERSON><PERSON> are the same person, as indicated by the fact that <PERSON> is addressed as <PERSON><PERSON> in the context of the novel. This is a common practice where characters are referred to by different names or nicknames.", "novel": "Pride and Prejudice"}, {"query_id": "107", "query": "In novel Pride and Prejudice, in which chapter does there exist a sentence with the same or similar meaning as 'Let's keep quiet about this issue forever. '?", "gt_answer": "The sentence with the same or similar meaning as \"Let's keep quiet about this issue forever\" can be found in Chapter XX of the novel Pride and Prejudice.", "novel": "Pride and Prejudice"}, {"query_id": "108", "query": "In novel Pride and Prejudice, in which chapter does there exist a sentence with the same or similar meaning as 'There must be some justification for exposing him in such a horrible way.'?", "gt_answer": "Chapter XL contains a sentence with a similar meaning to \"There must be some justification for exposing him in such a horrible way.\" The sentence in the chapter is \"Surely there can be no occasion for exposing him so dreadfully.\"", "novel": "Pride and Prejudice"}, {"query_id": "109", "query": "Who is old <PERSON><PERSON><PERSON>?", "gt_answer": "<PERSON> is the late Mr. <PERSON>'s steward.", "novel": "Pride and Prejudice"}, {"query_id": "110", "query": "When would the next ball be after the complaint of <PERSON>'s coughing?", "gt_answer": "The next ball would be to-morrow fortnight after the complaint of <PERSON>'s coughing.", "novel": "Pride and Prejudice"}, {"query_id": "111", "query": "In novel Pride and Prejudice, the sentence 'He was quite young, wonderfully handsome, extremely agreeable.' is to describe whom?", "gt_answer": "The sentence \"He was quite young, wonderfully handsome, extremely agreeable.\" describes Mr. <PERSON><PERSON>, who was highly regarded by Sir <PERSON> and was expected to attend the next assembly with a large party, creating lively hopes about his presence.", "novel": "Pride and Prejudice"}, {"query_id": "112", "query": "How many people on the whole would Mr<PERSON> bring in the large party held in London?", "gt_answer": "Mr. <PERSON> would bring a total of 19 people to the large party held in London, consisting of twelve ladies and seven gentlemen.", "novel": "Pride and Prejudice"}, {"query_id": "113", "query": "What has Mr<PERSON> <PERSON> done that made a sharp contrast between Mr. <PERSON> had soon made himself acquainted with all the principal people in the room?", "gt_answer": "Mr. <PERSON> danced only once with Mrs<PERSON> and once with <PERSON>, declined being introduced to any other lady, and spent the rest of the evening walking about the room, speaking occasionally to one of his own party. This behavior was in sharp contrast to Mr. <PERSON><PERSON>, who had soon made himself acquainted with all the principal people in the room, was lively and unreserved, danced every dance, was angry that the ball closed so early, and talked of giving one himself at Netherfield.", "novel": "Pride and Prejudice"}, {"query_id": "114", "query": "How many times have Mr <PERSON> and <PERSON> drink a bottle together?", "gt_answer": "Mr <PERSON> and Mr <PERSON> have drunk a bottle together once.", "novel": "The History of <PERSON>"}, {"query_id": "115", "query": "Who are in the The Blifil family?", "gt_answer": "The <PERSON><PERSON><PERSON><PERSON> family consists of Master <PERSON><PERSON><PERSON><PERSON>, Captain <PERSON><PERSON><PERSON><PERSON>, and Miss <PERSON>. Captain <PERSON><PERSON><PERSON><PERSON> is married to Miss <PERSON>, a young lady of great beauty, merit, and fortune. Miss <PERSON> gave birth to a fine boy eight months after their marriage.", "novel": "The History of <PERSON>"}, {"query_id": "116", "query": "In which year does the earliest event happen, and in which year does the latest even happen?", "gt_answer": "The earliest event happens in the year 1657, when the narrator was born in a village of Somersetshire called Mark. The latest event happens in the year 1747, when an advertisement was published on February 1st.", "novel": "The History of <PERSON>"}, {"query_id": "117", "query": "In novel The History of <PERSON>, has someone ever danced? If so, how many times has this plot happened?", "gt_answer": "Yes, there has been a dancing scene in the novel \"The History of <PERSON>.\" Specifically, <PERSON><PERSON> came dancing and capering into the room, filled with joy, exclaiming that someone had been found. This plot has happened once.", "novel": "The History of <PERSON>"}, {"query_id": "118", "query": "Who is Miss <PERSON>?", "gt_answer": "Miss <PERSON> is the woman who is involved in a marriage treaty with <PERSON><PERSON>, who has received an annual settlement of £50 from <PERSON> and has successfully re-established his school with better encouragement than before. This marriage treaty is likely to be successful due to the mediation of <PERSON>.", "novel": "The History of <PERSON>"}, {"query_id": "119", "query": "What is the relationship between  Miss <PERSON> and Captain <PERSON><PERSON><PERSON><PERSON>?", "gt_answer": "Captain <PERSON><PERSON><PERSON><PERSON> and Miss <PERSON> were a married couple. Eight months after their wedding, Miss <PERSON>, a young lady of great beauty, merit, and fortune, gave birth to a fine boy due to a fright.", "novel": "The History of <PERSON>"}, {"query_id": "120", "query": "How many times has <PERSON> helped <PERSON><PERSON>?", "gt_answer": "<PERSON> has helped <PERSON><PERSON> once, as he assisted him upon his legs and then looked him steadfastly in the face, expressing his surprise at their unexpected encounter.", "novel": "The History of <PERSON>"}, {"query_id": "121", "query": "It was in which month, when Mr. <PERSON> walked forth on the terrace, where the dawn opened every minute that lovely prospect we have before described to his eye?", "gt_answer": "Mr. <PERSON> walked forth on the terrace in the middle of May, where the dawn opened every minute that lovely prospect we have before described to his eye.", "novel": "The History of <PERSON>"}, {"query_id": "122", "query": "When the approach of Mrs. <PERSON> was proclaimed through the street, what the inhabitants do?", "gt_answer": "When the approach of Mrs. <PERSON> was proclaimed through the street, all the inhabitants ran trembling into their houses, each matron dreading lest the visit should fall to her lot.", "novel": "The History of <PERSON>"}, {"query_id": "123", "query": "When the first time <PERSON> appeared, what Mr <PERSON> do?", "gt_answer": "When <PERSON> first appeared, Mr. <PERSON><PERSON> took her into his study and spoke to her, saying, \"You know, child, it is in my power as a magistrate, to punish you very rigorously for what you have done.\"", "novel": "The History of <PERSON>"}, {"query_id": "124", "query": "When Mr. <PERSON> had retired to his study with whom, as hath been seen, Mrs. <PERSON>, with the good housekeeper, had betaken themselves to a post next adjoining to the said study?", "gt_answer": "<PERSON> was the person with whom Mr. <PERSON> had retired to his study, as hath been seen. Mrs. <PERSON>, along with the good housekeeper, had positioned themselves at a post next adjoining to the said study.", "novel": "The History of <PERSON>"}, {"query_id": "125", "query": "<PERSON> was, however, by the care and goodness of whom, soon removed out of the reach of reproach?", "gt_answer": "<PERSON> was, however, soon removed out of the reach of reproach by the care and goodness of Mr <PERSON>.", "novel": "The History of <PERSON>"}, {"query_id": "126", "query": "In novel The History of <PERSON>, at happens so that the newspapers are presently filled with it?", "gt_answer": "When a great prize happens to be drawn, the newspapers are presently filled with it, and the world is sure to be informed at whose office it was sold. Commonly, two or three different offices lay claim to the honor of having disposed of it, suggesting that certain brokers are in the secrets of <PERSON> and her cabinet council.", "novel": "The History of <PERSON>"}, {"query_id": "127", "query": "When Mr. <PERSON><PERSON> bore all patiently; but why he could not help laying claim to his blood?", "gt_answer": "When his wife appealed to the blood on her face as evidence of his barbarity, Mr. <PERSON><PERSON> could not help laying claim to his own blood, for so it really was.", "novel": "The History of <PERSON>"}, {"query_id": "128", "query": "Why the wife of <PERSON><PERSON> repent heartily of the evidence she had given against <PERSON><PERSON>?", "gt_answer": "The wife of <PERSON><PERSON> repented heartily of the evidence she had given against <PERSON><PERSON> when she found Mrs. <PERSON> had deceived her and refused to make any application to Mr. <PERSON> on her behalf.", "novel": "The History of <PERSON>"}, {"query_id": "129", "query": "In novel The History of Rome, who are in the The king family?", "gt_answer": "King <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are in the king family. King <PERSON><PERSON><PERSON><PERSON> was the son of <PERSON><PERSON><PERSON><PERSON>, who was the ruler of the Molossians. <PERSON><PERSON><PERSON><PERSON> was spared as a kinsman and faithful vassal by <PERSON> but was later drawn into the complex Macedonian family politics, ultimately losing both his kingdom and his life.", "novel": "The History of Rome"}, {"query_id": "130", "query": "In novel The History of Rome, has someone ever screamed in the novel? If so, how many times have they screamed?", "gt_answer": "Yes, someone has screamed in the novel \"The History of Rome,\" and it has happened once. <PERSON><PERSON><PERSON> is noted for his powerful screaming voice, which is the only instance of screaming mentioned.", "novel": "The History of Rome"}, {"query_id": "131", "query": "In novel The History of Rome, has any event ever been celebrated? If so, how many times has this plot happened?", "gt_answer": "Yes, there has been an event celebrated in the novel \"The History of Rome.\" Specifically, King <PERSON><PERSON> celebrated the marriage of his son <PERSON><PERSON><PERSON> with the sister of his new ally, <PERSON><PERSON><PERSON><PERSON>, the king of Armenia. This celebration occurred once, coinciding with the announcement of the victory of his vizier and the arrival of the cut-off head of <PERSON><PERSON><PERSON>.", "novel": "The History of Rome"}, {"query_id": "132", "query": "To all these fell to be added the cavalry, which consisted of how many horses; often when the army took the field, however, only the third part of the whole number was attached to it?", "gt_answer": "The cavalry consisted of 1800 horses; however, often when the army took the field, only the third part of the whole number was attached to it.", "novel": "The History of Rome"}, {"query_id": "133", "query": "When the Cnidians and who attempted about 175 to establish themselves at Lilybaeum?", "gt_answer": "The Rhodians, along with the Cnidians, attempted around 175 to establish themselves at Lilybaeum, the center of the Phoenician settlements in Sicily. However, they were expelled by the natives, specifically the Elymi of Segeste, who acted in concert with the Phoenicians.", "novel": "The History of Rome"}, {"query_id": "134", "query": "When the Phocaeans settled about 217 at which place in Corsica opposite to Caere, there appeared for expelling them a combined fleet of Etruscans and Carthaginians, numbering a hundred and twenty sail?", "gt_answer": "The Phocaeans settled about 217 at Alalia (Aleria) in Corsica, which is located opposite to Caere. To expel them, a combined fleet of Etruscans and Carthaginians, numbering a hundred and twenty sail, appeared.", "novel": "The History of Rome"}, {"query_id": "135", "query": "When the citizens of a conquered city were transported to Rome, who were also invited to take up their new abode there?", "gt_answer": "When the citizens of a conquered city were transported to Rome, the gods of that city were also invited to take up their new abode there.", "novel": "The History of Rome"}, {"query_id": "136", "query": "When the Greeks and who separated, there were still evidently no conventional signs of number?", "gt_answer": "When the Greeks and Italians separated, there were still evidently no conventional signs of number.", "novel": "The History of Rome"}, {"query_id": "137", "query": "When who's tradition speaks of halls in the Forum, where the boys and girls of quality were taught to read and write, already in the earliest times of the republic, the statement may be, but is not necessarily to be deemed an invention?", "gt_answer": "Roman tradition speaks of halls in the Forum, where the boys and girls of quality were taught to read and write, already in the earliest times of the republic, and the statement may be, but is not necessarily to be deemed an invention.", "novel": "The History of Rome"}, {"query_id": "138", "query": "In novel The History of Rome, when what had occurred, different districts supplied each other at these fairs with grain?", "gt_answer": "When bad harvests had occurred, different districts supplied each other at these fairs with grain.", "novel": "The History of Rome"}, {"query_id": "139", "query": "The first of these sources belongs to an age when which country was still regarded by the Greeks as a group of islands, and is certainly therefore very old?", "gt_answer": "Italy was still regarded by the Greeks as a group of islands, and is certainly therefore very old.", "novel": "The History of Rome"}, {"query_id": "140", "query": "The combined power of the Italians and who actually succeeded in substantially retaining the western half of the Mediterranean in their hands.?", "gt_answer": "The combined power of the Italians and Phoenicians actually succeeded in substantially retaining the western half of the Mediterranean in their hands.", "novel": "The History of Rome"}, {"query_id": "141", "query": "Who did not join in these hostilities against the Hellenes?", "gt_answer": "Latium did not join in these hostilities against the Hellenes.", "novel": "The History of Rome"}, {"query_id": "142", "query": "When who had substantially completed the new constitution projected by him for the state, he applied himself to a second and more difficult work?", "gt_answer": "<PERSON><PERSON><PERSON>, after having substantially completed the new constitution he had projected for the state, applied himself to a second and more difficult work.", "novel": "The History of Rome"}, {"query_id": "143", "query": "<PERSON><PERSON><PERSON> had at the beginning of the conflict retired into the temple of Minerva, and was there about to pierce himself with his sword, when his which friend seized his arm and besought him to preserve himself if possible for better times?", "gt_answer": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>'s friend, seized his arm and besought him to preserve himself if possible for better times when <PERSON><PERSON><PERSON> had at the beginning of the conflict retired into the temple of Minerva and was about to pierce himself with his sword.", "novel": "The History of Rome"}, {"query_id": "144", "query": "How many times have <PERSON> and <PERSON> gotten along with each other?", "gt_answer": "<PERSON> and <PERSON> have gotten along with each other only once.", "novel": "The Old Wives' Tale"}, {"query_id": "145", "query": "Who are in the <PERSON> family?", "gt_answer": "The <PERSON><PERSON> family consists of <PERSON>, Mrs. <PERSON>, and their daughter <PERSON>.", "novel": "The Old Wives' Tale"}, {"query_id": "146", "query": "What is the relationship between Mrs. <PERSON> and middle-aged fool?", "gt_answer": "Mrs. <PERSON><PERSON> and the middle-aged fool are the same person. Mrs. <PERSON><PERSON> herself acknowledges this when she reflects on her wild suspicions and irrational notions about <PERSON> and <PERSON>. She realizes that her suspicions are unfounded and calls herself a middle-aged fool for basing such thoughts on a brief encounter and a fanciful idea.", "novel": "The Old Wives' Tale"}, {"query_id": "147", "query": "Who is <PERSON>?", "gt_answer": "<PERSON> was a middle-aged and wealthy man who kept a busy establishment at the corner of the Rue Clausel.", "novel": "The Old Wives' Tale"}, {"query_id": "148", "query": "In novel The Old Wives' Tale, in which chapter does there exist a sentence with the same or similar meaning as 'She briefly noticed a sizable bathtub positioned beside the bed, and before she knew it, she found herself gently guided into its chilling, icy-cold water.'?", "gt_answer": "Chapter 5 contains a sentence with the same or similar meaning as 'She briefly noticed a sizable bathtub positioned beside the bed, and before she knew it, she found herself gently guided into its chilling, icy-cold water.'", "novel": "The Old Wives' Tale"}, {"query_id": "149", "query": "What is the relationship between <PERSON><PERSON> and <PERSON>?", "gt_answer": "<PERSON><PERSON> was the uncle of <PERSON>, who was harsh towards him.", "novel": "The Old Wives' Tale"}, {"query_id": "150", "query": "In novel The Old Wives' Tale, in which chapter does there exist a sentence with the same or similar meaning as 'However, the sheer disarray on the washstand, with a towel carelessly strewn across one of the cane chairs, induced a sense of impropriety in him, intensifying his casual restlessness.'?", "gt_answer": "The sentence with the same or similar meaning as 'However, the sheer disarray on the washstand, with a towel carelessly strewn across one of the cane chairs, induced a sense of impropriety in him, intensifying his casual restlessness.' exists in Chapter 1.", "novel": "The Old Wives' Tale"}, {"query_id": "151", "query": "In novel The Old Wives' Tale, has someone ever felf heart broken? If so, how many times has this plot happened?", "gt_answer": "Yes, in the novel \"The Old Wives' Tale,\" there is a character who experiences heartbreak. This plot point occurs once, as indicated by the description of a man who had eaten scarcely anything and went about with the face of a man dying of a broken heart.", "novel": "The Old Wives' Tale"}, {"query_id": "152", "query": "In novel The Old Wives' Tale, in which chapter does there exist a sentence with the same or similar meaning as The quiet denial of individuality had passed unnoticed, leaving little trace of his presence in either his room or the recollections of those who shared the house with him.?", "gt_answer": "Chapter 7 contains a sentence with a similar meaning to \"The quiet denial of individuality had passed unnoticed, leaving little trace of his presence in either his room or the recollections of those who shared the house with him.\"", "novel": "The Old Wives' Tale"}, {"query_id": "153", "query": "In novel The Old Wives' Tale, in which chapter does there exist a sentence with the same or similar meaning as ' However, he persisted in pestering her feet, leading to one of those ceaseless and monotonous arguments on a singular topic that frequently arises between two intellectual peers, where one is a young son of the household and the other a seasoned servant who deeply admires him.'?", "gt_answer": "Chapter 4 contains a sentence with a similar meaning to \"However, he persisted in pestering her feet, leading to one of those ceaseless and monotonous arguments on a singular topic that frequently arises between two intellectual peers, where one is a young son of the household and the other a seasoned servant who deeply admires him.\"", "novel": "The Old Wives' Tale"}, {"query_id": "154", "query": "In novel The Old Wives' Tale, it is widely believed that in which country, whenever a woman washes up, she washes up the product of the district?", "gt_answer": "In the novel The Old Wives' Tale, it is widely believed that in England, whenever a woman washes up, she washes up the product of the district.", "novel": "The Old Wives' Tale"}, {"query_id": "155", "query": "What did <PERSON> said after her hair was quite finished and knelt down?", "gt_answer": "<PERSON> said her prayers after her hair was quite finished and she knelt down.", "novel": "The Old Wives' Tale"}, {"query_id": "156", "query": "In novel The Old Wives' Tale, when who entered the room, the paralytic followed her with his nervous gaze until she had sat down on the end of the sofa at the foot of the bed?", "gt_answer": "When <PERSON> entered the room, the paralytic followed her with his nervous gaze until she had sat down on the end of the sofa at the foot of the bed.", "novel": "The Old Wives' Tale"}, {"query_id": "157", "query": "In novel The Old Wives' Tale, when who had eaten to his capacity, he took the Signal importantly from his pocket, posed his spectacles, and read the obituary all through in slow, impressive accents.?", "gt_answer": "Mr. <PERSON><PERSON><PERSON><PERSON>, after having eaten to his capacity, took the Signal importantly from his pocket, posed his spectacles, and read the obituary all through in slow, impressive accents.", "novel": "The Old Wives' Tale"}, {"query_id": "158", "query": "How many times have Mr<PERSON> and Mrs. <PERSON> had dinner together?", "gt_answer": "Mr. <PERSON> and Mrs. <PERSON><PERSON><PERSON> have had dinner together only once.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "159", "query": "Who are in the <PERSON><PERSON> family?", "gt_answer": "The <PERSON> family consists of <PERSON>, <PERSON>, <PERSON>, and Sir <PERSON>.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "160", "query": "In novel <PERSON><PERSON><PERSON>'s <PERSON>, in which chapter does there exist a sentence with the same or similar meaning as 'On this occasion, numerous candidates vied for Glenbogie. One of them was Mr. <PERSON>, whose candidacy was not self-asserted, as it would have been impractical, but rather advocated by <PERSON> on his behalf.'?", "gt_answer": "Chapter 14 of the novel <PERSON><PERSON><PERSON>'s <PERSON> contains a sentence with a similar meaning to \"On this occasion, numerous candidates vied for Glenbogie. One of them was Mr. <PERSON>, whose candidacy was not self-asserted, as it would have been impractical, but rather advocated by <PERSON> on his behalf.\"", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "161", "query": "In novel <PERSON><PERSON><PERSON>'s <PERSON>, in which chapter does there exist a sentence with the same or similar meaning as 'The younger of the <PERSON><PERSON> sisters, <PERSON>, who was considerably younger than her sister, chose to dedicate herself to <PERSON><PERSON><PERSON>, the artist whose death we commemorated in our opening line.'?", "gt_answer": "Chapter 1", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "162", "query": "In novel <PERSON><PERSON><PERSON>'s <PERSON>, in which chapter does there exist a sentence with the same or similar meaning as '<PERSON> had obtained this last boon from her father just two days before the wedding, sealed with her sweetest kiss.'?", "gt_answer": "Chapter 8 contains a sentence with a similar meaning to \"<PERSON> had obtained this last boon from her father just two days before the wedding, sealed with her sweetest kiss.\"", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "163", "query": "At the time <PERSON><PERSON><PERSON> died, how many daughters did he have?", "gt_answer": "<PERSON><PERSON><PERSON> had two daughters at the time of his death.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "164", "query": "How old was <PERSON><PERSON><PERSON> when her father died?", "gt_answer": "<PERSON><PERSON><PERSON> was nineteen years old when her father died.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "165", "query": "When it was decided who would go to Kingsbury Crescent the difference was very great?", "gt_answer": "When it was decided that <PERSON> would go to Kingsbury Crescent, the difference was very great.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "166", "query": "How much salary has Mr<PERSON> <PERSON><PERSON> received per annum?", "gt_answer": "Mr. <PERSON><PERSON> has received a salary of £900 per annum.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "167", "query": "When Mr. <PERSON><PERSON> died, how many years has it been since <PERSON><PERSON>'s marriage?", "gt_answer": "Mr. <PERSON><PERSON> died some ten years after his marriage.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "168", "query": "Who was brought to this house when Mr. <PERSON> had consented to share with Sir <PERSON> the burden left by the death of the improvident artist?", "gt_answer": "<PERSON> was brought to this house when Mr. <PERSON> had consented to share with Sir <PERSON> the burden left by the death of the improvident artist.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "169", "query": "When <PERSON>'s anger boiled within her when she was told of an excuse, and she declared to herself that she could never like whom?", "gt_answer": "<PERSON>'s anger boiled within her when she was told of an excuse, and she declared to herself that she could never like her aunt.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "170", "query": "When <PERSON> was taken to Kingsbury Crescent who was at once carried down to Glenbogie, and from thence there came letters twice a week for six weeks?", "gt_answer": "<PERSON><PERSON><PERSON> was at once carried down to Glenbogie when <PERSON> was taken to Kingsbury Crescent, and from there, letters came twice a week for six weeks.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "171", "query": "<PERSON> hinted who had pawned her diamonds, then she felt that her nearest and dearest relatives smelt abominably of money?", "gt_answer": "Lady <PERSON> had pawned her diamonds, which led <PERSON> to feel that her nearest and dearest relatives smelt abominably of money.", "novel": "<PERSON><PERSON><PERSON>'s Angel"}, {"query_id": "172", "query": "Who are in the The family Vavasor?", "gt_answer": "The family <PERSON><PERSON><PERSON> consists of <PERSON>, her first cousin <PERSON>, and <PERSON>.", "novel": "Can You Forgive Her"}, {"query_id": "173", "query": "In novel Can You Forgive Her, in which chapter does there exist a sentence with the same or similar meaning as ' Idleness-absolute idleness-was not one of <PERSON>'s many flaws.'?", "gt_answer": "Chapter 4 contains a sentence that conveys the same meaning as 'Idleness-absolute idleness-was not one of <PERSON>'s many flaws.' Specifically, it states that <PERSON> had many faults, but idleness-absolute idleness-was not one of them.", "novel": "Can You Forgive Her"}, {"query_id": "174", "query": "In novel Can You Forgive Her, in which chapter does there exist a sentence with the same or similar meaning as ' 'Come on, Mr. <PERSON><PERSON><PERSON>!' The publican's expression was highly expressive as he made this plea, directing a compelling look at the attorney.'?", "gt_answer": "Chapter 13 contains a sentence with a similar meaning to \"'Come on, Mr. <PERSON><PERSON><PERSON>!' The publican's expression was highly expressive as he made this plea, directing a compelling look at the attorney.\"", "novel": "Can You Forgive Her"}, {"query_id": "175", "query": "In novel Can You Forgive Her, has someone ever fallen from a chair? If so, how many times has this plot happened?", "gt_answer": "Yes, in the novel \"Can You Forgive Her,\" someone has indeed fallen from a chair. This incident has happened once, as described when her aunt hastened up to her side just in time to catch her as she was falling from her chair.", "novel": "Can You Forgive Her"}, {"query_id": "176", "query": "In novel Can You Forgive Her, in which chapter does there exist a sentence with the same or similar meaning as 'There can, in any case, be no question that Mr. <PERSON> was in an ideal situation and more joyful with his practically ostensible work than he would have been without it.'?", "gt_answer": "Chapter 1, where it is stated that there can be no doubt that Mr. <PERSON><PERSON> was better off and happier with his almost nominal employment than he would have been without it.", "novel": "Can You Forgive Her"}, {"query_id": "177", "query": "How many times have <PERSON> and <PERSON> dining?", "gt_answer": "<PERSON> and <PERSON> have dined together once, as indicated by the evening before they started for Switzerland when <PERSON> and <PERSON> walked from Queen Anne Street, where they had been dining with <PERSON>, to Mrs<PERSON> <PERSON><PERSON>'s house.", "novel": "Can You Forgive Her"}, {"query_id": "178", "query": "In novel Can You Forgive Her, when a man marries an heiress for her money, if that money be within her control, as was the case with who's fortune, it is generally well for the speculating lover that the lady's friends should quarrel with him and with her.?", "gt_answer": "Miss <PERSON><PERSON><PERSON>'s fortune was within her own control, and in such cases, it is generally advantageous for the speculating lover if the lady's friends quarrel with both him and her.", "novel": "Can You Forgive Her"}, {"query_id": "179", "query": "How old <PERSON> was when she had the full command of her fortune?", "gt_answer": "<PERSON> was twenty-one when she had the full command of her own fortune.", "novel": "Can You Forgive Her"}, {"query_id": "180", "query": "When she induced her father, how long he had lived in lodgings, to take a small house in Queen Anne Street?", "gt_answer": "For the last fifteen years, her father had lived in lodgings before she persuaded him to take a small house in Queen Anne Street.", "novel": "Can You Forgive Her"}, {"query_id": "181", "query": "When Lady <PERSON> got up to go away, who kissed her as was customary with them?", "gt_answer": "<PERSON> kissed <PERSON> when she got up to go away, as was customary with them, and the old lady, as she went, uttered her customary valediction.", "novel": "Can You Forgive Her"}, {"query_id": "182", "query": "When they got upon the bridge, <PERSON> and <PERSON> were together, and where <PERSON> was?", "gt_answer": "<PERSON> strolled behind them, close to them, but not taking any part in their conversation, as though he had merely gone with them as an escort.", "novel": "Can You Forgive Her"}, {"query_id": "183", "query": "Mrs. <PERSON><PERSON> lingered to say a word or two whom, of the manner in which <PERSON><PERSON>'s body was treated when <PERSON><PERSON> was lying dead, and then she followed her brother and niece?", "gt_answer": "Mrs. <PERSON><PERSON> lingered to say a word or two to the nurse about the manner in which <PERSON><PERSON>'s body was treated when <PERSON><PERSON> was lying dead, and then she followed her brother and niece.", "novel": "Can You Forgive Her"}, {"query_id": "184", "query": "<PERSON> when left the room he had insulted whom?", "gt_answer": "<PERSON>, when he left the room, had insulted the lawyer.", "novel": "Can You Forgive Her"}, {"query_id": "185", "query": "When <PERSON><PERSON><PERSON> further asked whether there would be a gig, why did the boy stare at him?", "gt_answer": "The boy stared at <PERSON><PERSON><PERSON> because he did not know a gig by that name.", "novel": "Can You Forgive Her"}, {"query_id": "186", "query": "From which country had Mr. <PERSON> returned when he was back as a priest?", "gt_answer": "Mr. <PERSON> had returned from Italy when he came back as a priest.", "novel": "Les Miserables"}, {"query_id": "187", "query": "On one day, who had come to visit <PERSON><PERSON>'s uncle, the worthy <PERSON><PERSON><PERSON>, who was waiting in the anteroom, found himself present when His Majesty passed?", "gt_answer": "The Emperor had come to visit <PERSON><PERSON>'s uncle, the worthy <PERSON><PERSON><PERSON>, who was waiting in the anteroom and found himself present when His Majesty passed.", "novel": "Les Miserables"}, {"query_id": "188", "query": "How many old women accompanied <PERSON> when the first time that the Bishop had gone to the trip?", "gt_answer": "The Bishop was accompanied by two old women when he went on the trip for the first time.", "novel": "Les Miserables"}, {"query_id": "189", "query": "In novel <PERSON>, when who dies in the family, the boys go off to seek their fortunes, leaving the property to the girls, so that they may find husbands?", "gt_answer": "When the father of a family dies in the novel Les Misérables, the boys go off to seek their fortunes, leaving the property to the girls, so that they may find husbands.", "novel": "Les Miserables"}, {"query_id": "190", "query": "Was <PERSON>cier young or old, a man or a woman?", "gt_answer": "<PERSON> is an old man who is wealthy and avaricious, managing to be both an ultra-royalist and an ultra-Voltairian.", "novel": "Les Miserables"}, {"query_id": "191", "query": "In novel Les Misérables, there was a chair in the detached alcove. How many legs did the chair have that it was service only when propped against the wall?", "gt_answer": "The chair in the detached alcove in the novel Les Misérables had three legs, making it serviceable only when propped against the wall.", "novel": "Les Miserables"}, {"query_id": "192", "query": "How many candles <PERSON> lighted when the Bishop had his last dinner with <PERSON>?", "gt_answer": "Madame <PERSON> lighted two candles when the Bishop had his last dinner with <PERSON>.", "novel": "Les Miserables"}, {"query_id": "193", "query": "What was the name of the song that the Bishop gave when he have to depart from house?", "gt_answer": "The name of the song that the Bishop gave when he had to depart from the house was \"Te Deum.\" When the time of his departure approached, he resolved to chant a Te Deum pontifically.", "novel": "Les Miserables"}, {"query_id": "194", "query": "When the Bishop had dinner, what was the name of the person who usually lighted candles and set the candlesticks for the Bishop?", "gt_answer": "Madame <PERSON><PERSON> was the person who usually lighted the candles and set the candlesticks for the Bishop when he had dinner.", "novel": "Les Miserables"}, {"query_id": "195", "query": "Marquis <PERSON> was a wealthy or a poor man?", "gt_answer": "<PERSON> was a wealthy man, known for being both avaricious and holding contradictory beliefs as an ultra-royalist and an ultra-Voltairian.", "novel": "Les Miserables"}, {"query_id": "196", "query": "For stealing what did <PERSON> spend 19 years in hard labor?", "gt_answer": "<PERSON> spent 19 years in hard labor for stealing a loaf of bread. This incident occurred in 1795 when he broke a pane of glass and took the bread from an inhabited house at night. He was subsequently taken before the tribunals of the time and sentenced for theft and breaking and entering. He was released in October 1815, having served his sentence.", "novel": "Les Miserables"}, {"query_id": "197", "query": "In novel <PERSON>, who was called the lark by the neighbors?", "gt_answer": "<PERSON><PERSON><PERSON>, who was once pretty and rosy upon her arrival in the house, had become thin and pale over time. She was affectionately referred to as the Lark by the neighbors.", "novel": "Les Miserables"}, {"query_id": "198", "query": "What's the end of Then<PERSON><PERSON>?", "gt_answer": "<PERSON><PERSON><PERSON> at last obtained a sum of money and went to America to trade slaves. Two days after the events being narrated, he set out for America under a false name with his daughter <PERSON><PERSON><PERSON>, furnished with a draft on New York for twenty thousand francs, thanks to <PERSON>' care. Despite the change in location, <PERSON><PERSON><PERSON> remained morally wretched and continued his corrupt ways, setting up as a slave-dealer with <PERSON>' money.", "novel": "Les Miserables"}, {"query_id": "199", "query": "What is the relationship between <PERSON> and <PERSON><PERSON><PERSON>?", "gt_answer": "<PERSON> used to pretend to be <PERSON><PERSON><PERSON>, so in this novel, they refer to the same person. <PERSON> was officially installed under the name <PERSON><PERSON><PERSON>, and he was even recognized as a brother of <PERSON>.", "novel": "Les Miserables"}, {"query_id": "200", "query": "Has Mr <PERSON> really killed Mrs <PERSON><PERSON>?", "gt_answer": "No, she killed herself. The evidence showed that Mrs. <PERSON>, while attempting to cross the railway line, was knocked down by the engine of the ten o'clock slow train from Kingstown, sustaining fatal injuries to her head and right side. The Deputy Coroner described it as a most painful case and expressed great sympathy with Captain <PERSON><PERSON> and his daughter, emphasizing that no blame was attached to anyone and urging the railway company to take measures to prevent similar accidents in the future.", "novel": "Dubliners"}, {"query_id": "201", "query": "How many times have <PERSON><PERSON>'s sister and 'I' in Araby communicate with each other,?", "gt_answer": "<PERSON><PERSON>'s sister and 'I' in <PERSON><PERSON> communicated with each other only once. When she spoke to him for the first time, he was so confused that he did not know what to answer, and during their conversation, she turned a silver bracelet round and round her wrist.", "novel": "Dubliners"}, {"query_id": "202", "query": "What is the relationship between Mrs <PERSON> and <PERSON>?", "gt_answer": "Mrs. <PERSON><PERSON> is <PERSON>'s mother. When the Irish Revival began to gain momentum, Mrs. <PERSON><PERSON> decided to leverage her daughter's name and brought an Irish teacher to their home. <PERSON>, along with her sister, participated in the cultural movement by sending Irish picture postcards to their friends, who reciprocated with other Irish-themed postcards.", "novel": "Dubliners"}, {"query_id": "203", "query": "In novel Dubliners, in which chapter does there exist a sentence with the same or similar meaning as 'He found that all the other suburbs of Dublin are like a affected, stingy and morden man. '?", "gt_answer": "The sentence with the same or similar meaning as \"He found that all the other suburbs of Dublin are like an affected, stingy and modern man\" exists in the chapter \"A Painful Case\" of the novel Dubliners.", "novel": "Dubliners"}, {"query_id": "204", "query": "Is the character Mr <PERSON> in the Dubliners alone? Please provide an excerpt of 1 to 2 sentences from the original text as supportig evidence.", "gt_answer": "Yes, Mr. <PERSON> is alone. He had neither companions nor friends, church nor creed. He lived his spiritual life without any communion with others, visiting his relatives at Christmas and escorting them to the cemetery when they died.", "novel": "Dubliners"}, {"query_id": "205", "query": "What religion does Mrs. <PERSON> believe in?", "gt_answer": "Mrs. <PERSON><PERSON> believes in Catholicism. Her beliefs are steady and centered around the Sacred Heart, which she considers the most generally useful of all Catholic devotions. She also approves of the sacraments. While her faith is primarily confined to her immediate surroundings, she is capable of believing in other aspects of Catholicism, such as the banshee and the Holy Ghost, if necessary.", "novel": "Dubliners"}, {"query_id": "206", "query": "When <PERSON> was hearing the four pages of Roman History clumsy <PERSON> was discovered with a copy of which story?", "gt_answer": "Clumsy <PERSON> was discovered with a copy of The Halfpenny Marvel when <PERSON> was hearing the four pages of Roman History.", "novel": "Dubliners"}, {"query_id": "207", "query": "When <PERSON> sat at his desk in the King's Inns he thought how many years had been spent?", "gt_answer": "<PERSON> sat at his desk in the King's Inns and reflected on the changes that had occurred over the eight years that had passed.", "novel": "Dubliners"}, {"query_id": "208", "query": "<PERSON> used to lecture <PERSON><PERSON><PERSON><PERSON> at which time of the week when he had a sore head and a fur on his tongue.", "gt_answer": "<PERSON> used to lecture <PERSON><PERSON><PERSON><PERSON> on Sunday mornings when <PERSON><PERSON><PERSON><PERSON> had a sore head and a fur on his tongue.", "novel": "Dubliners"}, {"query_id": "209", "query": "How old was <PERSON> when he started living in London?", "gt_answer": "<PERSON> was twenty years old when he started living in London.", "novel": "Dubliners"}, {"query_id": "210", "query": "The bell rang furiously and, at that time who went to the tube, a furious voice called out in a piercing North of Ireland accent: Send Farrington here!?", "gt_answer": "<PERSON> went to the tube when the bell rang furiously, and a furious voice called out in a piercing North of Ireland accent: \"Send <PERSON><PERSON> here!\"", "novel": "Dubliners"}, {"query_id": "211", "query": "In novel Dubliners, who said 'Go!' so that each was to try to bring down the other's hand onto the table?", "gt_answer": "<PERSON> said \"Go!\" so that each was to try to bring down the other's hand onto the table.", "novel": "Dubliners"}, {"query_id": "212", "query": "When the Irish Revival began to be appreciable who determined to take advantage of her daughter's name and brought an Irish teacher to the house?", "gt_answer": "Mrs <PERSON><PERSON>, when the Irish Revival began to be appreciable, determined to take advantage of her daughter's name and brought an Irish teacher to the house.", "novel": "Dubliners"}, {"query_id": "213", "query": "From where had <PERSON> brought a purse to <PERSON> five years before?", "gt_answer": "<PERSON> had brought a purse to <PERSON> five years before from Belfast on a Whit-Monday trip when he and <PERSON><PERSON> had gone there.", "novel": "Dubliners"}, {"query_id": "214", "query": "Who saw <PERSON><PERSON> kiss <PERSON>?", "gt_answer": "<PERSON> saw <PERSON><PERSON> kiss <PERSON>. She looked between the leaves and witnessed the kiss, feeling a profound emotional reaction as she observed the scene.", "novel": "The Waves"}, {"query_id": "215", "query": "What's the job of <PERSON>'s father?", "gt_answer": "<PERSON>'s father works as a banker in Brisbane, and <PERSON> mentions this while noting that he speaks with an Australian English accent.", "novel": "The Waves"}, {"query_id": "216", "query": "How did <PERSON><PERSON><PERSON> die?", "gt_answer": "<PERSON><PERSON><PERSON> died when his horse tripped, causing him to fall off and suffer fatal injuries.", "novel": "The Waves"}, {"query_id": "217", "query": "Who went to India?", "gt_answer": "<PERSON><PERSON><PERSON> went to India.", "novel": "The Waves"}, {"query_id": "218", "query": "What did <PERSON> see when he leaned over the parapet?", "gt_answer": "When <PERSON> leaned over the parapet, he saw the fin of a porpoise far out on the horizon, emerging from a vast expanse of water.", "novel": "The Waves"}, {"query_id": "219", "query": "What happened to <PERSON>'s mother?", "gt_answer": "<PERSON>'s mother passed away due to cancer.", "novel": "The Waves"}, {"query_id": "220", "query": "How did <PERSON> behave when he went to school for the first time?", "gt_answer": "<PERSON> behaved composed and easy when he went to school for the first time. He walked with a sense of calmness and confidence, swinging his bags as he moved.", "novel": "The Waves"}, {"query_id": "221", "query": "In novel <PERSON> Waves, who is the headmaster?", "gt_answer": "<PERSON>, the Headmaster, has a nose like a mountain at sunset, and a blue cleft in his chin, like a wooded ravine, which some tripper has fired; like a wooded ravine seen from the train window.", "novel": "The Waves"}, {"query_id": "222", "query": "What happened to <PERSON> when <PERSON><PERSON><PERSON> died?", "gt_answer": "<PERSON> experienced a mix of emotions when <PERSON><PERSON><PERSON> died because, at the same time, his son was born.", "novel": "The Waves"}, {"query_id": "223", "query": "What kind of flower was on the table of the restarurant where <PERSON> and <PERSON><PERSON><PERSON> had dinner together?", "gt_answer": "The red carnation that stood in the vase on the table of the restaurant where <PERSON> and <PERSON><PERSON><PERSON> had dinner together is described as having become a six-sided flower, made of six lives.", "novel": "The Waves"}, {"query_id": "224", "query": "How did <PERSON> feel at her first night in school?", "gt_answer": "<PERSON> felt sad and uncomfortable on her first night at school because she was away from her father and home. Her eyes swelled and pricked with tears, and she expressed her hatred for the smell of pine and linoleum, the wind-bitten shrubs, the sanitary tiles, the cheerful jokes, and the glazed look of everyone around her.", "novel": "The Waves"}, {"query_id": "225", "query": "In novel The Waves, who is a hunchback?", "gt_answer": "<PERSON> is the hunchback in the novel \"The Waves.\" <PERSON><PERSON><PERSON> mentions that when <PERSON> passes by, talking to the clergyman, others laugh and imitate her hunch behind her back. Despite this, everything changes and becomes luminous, and <PERSON><PERSON> leaps higher when <PERSON> passes.", "novel": "The Waves"}, {"query_id": "226", "query": "What does <PERSON><PERSON> hate?", "gt_answer": "<PERSON><PERSON> hates darkness, sleep, and night, and she lies longing for the day to come. She wishes that the week could be all one day without divisions. When she wakes early, often due to the birds, she lies in bed watching the brass handles on the cupboard grow clear, followed by the basin and then the towel-horse.", "novel": "The Waves"}, {"query_id": "227", "query": "How many times have <PERSON> and <PERSON> walked together?", "gt_answer": "<PERSON> and <PERSON> have walked together once. <PERSON> believed they had discussed Mr<PERSON> sufficiently during that walk.", "novel": "<PERSON>"}, {"query_id": "228", "query": "Who are in the <PERSON> family?", "gt_answer": "The <PERSON> family consists of Mrs. <PERSON> and Mr. <PERSON>.", "novel": "<PERSON>"}, {"query_id": "229", "query": "Who is <PERSON>?", "gt_answer": "<PERSON> was the teacher who was invited by Mrs. <PERSON> to supper along with the other two teachers, <PERSON> and <PERSON>.", "novel": "<PERSON>"}, {"query_id": "230", "query": "What is the relationship between Mr. <PERSON> and <PERSON>?", "gt_answer": "Mr. <PERSON><PERSON> was the oldest friend of <PERSON>, being a very old and intimate friend of her family. He was particularly connected with the family as the elder brother of <PERSON>'s husband.", "novel": "<PERSON>"}, {"query_id": "231", "query": "How many times has <PERSON> run to <PERSON>?", "gt_answer": "<PERSON> has run to <PERSON> once, as indicated by the instance where <PERSON> came running to her with a smiling face and in a flutter of spirits.", "novel": "<PERSON>"}, {"query_id": "232", "query": "Through how many generations had Mr<PERSON>'s family been rising into gentility and property?", "gt_answer": "Mr. <PERSON>'s family had been rising into gentility and property for the last two or three generations.", "novel": "<PERSON>"}, {"query_id": "233", "query": "<PERSON> has been meaning to read more ever since when?", "gt_answer": "<PERSON> has been meaning to read more ever since she was twelve years old, indicating that her desire to increase her reading habits has been present since her early adolescence.", "novel": "<PERSON>"}, {"query_id": "234", "query": "At which age had <PERSON> the misfortune of being able to answer questions which puzzled her sister at seventeen?", "gt_answer": "<PERSON> had the misfortune of being able to answer questions that puzzled her sister at seventeen when she was ten years old.", "novel": "<PERSON>"}, {"query_id": "235", "query": "Fill in the blank according to original text: 'during the ten days of their stay at Hartfield it was not to be expected-she did not herself expect-that any thing beyond occasional, fortuitous assistance could be afforded by her to the lovers.'", "gt_answer": "During the ten days of their stay at Hartfield, it was not to be expected—she did not herself expect—that anything beyond occasional, fortuitous assistance could be afforded by her to the lovers.", "novel": "<PERSON>"}, {"query_id": "236", "query": "The only visit from <PERSON> for how many days?", "gt_answer": "The only visit from <PERSON> was for ten days.", "novel": "<PERSON>"}, {"query_id": "237", "query": "What was the color of <PERSON>'s eyes?", "gt_answer": "<PERSON>'s eyes were blue, complementing her fair complexion, light hair, and regular features. She had a look of great sweetness, which, along with her pleasing manners, made a positive impression on <PERSON> by the end of the evening.", "novel": "<PERSON>"}, {"query_id": "238", "query": "How many children did Mr. and Mrs. <PERSON> have?", "gt_answer": "Mr. and Mrs. <PERSON> had five children, as indicated by the passage describing their journey to Hartfield with their five children and a sufficient number of nursery-maids.", "novel": "<PERSON>"}, {"query_id": "239", "query": "<PERSON> was called downstairs to Mr. <PERSON>, who could not stay five minutes, and wanted particularly to speak with her after about how many days of Mrs<PERSON>'s decease?", "gt_answer": "<PERSON> was called downstairs to Mr. <PERSON>, who could not stay five minutes, and wanted particularly to speak with her about ten days after Mrs<PERSON>'s decease.", "novel": "<PERSON>"}, {"query_id": "240", "query": "What is the relationship between <PERSON>   and Mrs. <PERSON>'s?", "gt_answer": "<PERSON> and Mrs. <PERSON><PERSON> are the same person. Mrs. <PERSON><PERSON>'s relationship with her children, particularly her sons, is a central theme in the novel. Her intimacy with her second son is described as subtle and fine, and she has a significant emotional connection with her son <PERSON>, around whom her life becomes centered.", "novel": "Sons and Lovers"}, {"query_id": "241", "query": "In novel Sons and Lovers, explain the meaning or implication of the symbol or metaphor 'air that was poisoned' within one sentence.", "gt_answer": "The phrase \"air that was poisoned\" in the novel Sons and Lovers implies an atmosphere filled with the poison of disappointments, creating a pervasive sense of misery and dreariness among the children, leaving them disconsolate and aimless.", "novel": "Sons and Lovers"}, {"query_id": "242", "query": "In novel Sons and Lovers, in which chapter does there exist a sentence with the same or similar meaning as ' filled with the poison of disappointments'?", "gt_answer": "Chapter 2, filled with the poison of disappointments, contains a sentence where the children breathed the air that was poisoned, and they felt dreary.", "novel": "Sons and Lovers"}, {"query_id": "243", "query": "In novel Sons and Lovers, in which chapter does there exist a sentence with the same or similar meaning as 'From the edge of the sandhills, a huge orange moon was gazing at the people'?", "gt_answer": "In Chapter 7 of the novel \"Sons and Lovers,\" there is a sentence that conveys a similar meaning to \"From the edge of the sandhills, a huge orange moon was gazing at the people,\" describing an enormous orange moon staring at them from the rim of the sandhills.", "novel": "Sons and Lovers"}, {"query_id": "244", "query": "In novel Sons and Lovers, explain the meaning or implication of the symbol or metaphor 'gin-pits' within one sentence.", "gt_answer": "The symbol \"gin-pits\" in the novel \"Sons and Lovers\" refers to a shallow mine where the hoisting is done by a gin, representing the small, traditional mining operations that are being overshadowed and replaced by larger, more industrialized mines.", "novel": "Sons and Lovers"}, {"query_id": "245", "query": "Has someone ever died at the birth of her first baby? If so, how many times has this plot happened?", "gt_answer": "Yes, someone has died at the birth of her first baby, and this plot has happened once. Mr. <PERSON>, a young and very poor Congregational clergyman, experienced this tragedy when his wife died during the birth of their first child, leaving him alone in the manse.", "novel": "Sons and Lovers"}, {"query_id": "246", "query": "What is the relationship between <PERSON> and <PERSON>?", "gt_answer": "<PERSON> and <PERSON> are brother and sister. This is evident from the way <PERSON> whispers <PERSON>'s name to wake him up and the familiarity and concern in her actions, as she stands in the darkness in her white nightdress with her long plait of hair down her back.", "novel": "Sons and Lovers"}, {"query_id": "247", "query": "In novel Sons and Lovers, in which chapter does there exist a sentence with the same or similar meaning as 'The children's minds were filled with the poison of disappointments, which also makes a cheerless atomosphere'?", "gt_answer": "Chapter 2 contains a sentence that conveys a similar meaning to \"The children's minds were filled with the poison of disappointments, which also makes a cheerless atmosphere,\" as it describes the children breathing air that was poisoned and feeling dreary.", "novel": "Sons and Lovers"}, {"query_id": "248", "query": "How many times have <PERSON> and <PERSON> practically separated?", "gt_answer": "<PERSON> and <PERSON> have practically separated once.", "novel": "Sons and Lovers"}, {"query_id": "249", "query": "Who lived in the cottages that stood by the brookside on Greenhill Lane and where did they work in the test of the novel Sons and Lovers?", "gt_answer": "There lived the colliers who worked in the little gin-pits two fields away in the cottages that stood by the brookside on Greenhill Lane.", "novel": "Sons and Lovers"}, {"query_id": "250", "query": "How many years ago the coal and iron field of Nottinghamshire and Derbyshire was discovered.in the test of the novel Sons and Lovers?", "gt_answer": "The coal and iron field of Nottinghamshire and Derbyshire was discovered sixty years ago when a sudden change took place, and gin-pits were replaced by the large mines of the financiers.", "novel": "Sons and Lovers"}, {"query_id": "251", "query": "How many pits were thereafter new mines had been sunken in the test of the novel Sons and Lovers?", "gt_answer": "There were six pits working after Carston, Waite and Co. found they had struck on a good thing and sunk new mines down the valleys of the brooks from Selby and Nuttall.", "novel": "Sons and Lovers"}, {"query_id": "252", "query": "When did Mrs<PERSON> <PERSON><PERSON>'s husband come home when she was sewing and waiting for him in the test of the novel Sons and Lovers?", "gt_answer": "Mrs. <PERSON><PERSON>'s husband came home at half-past eleven when she was sewing and waiting for him in the novel \"Sons and Lovers.\" His cheeks were very red and shiny above his black moustache, and his head nodded slightly, indicating that he was pleased with himself.", "novel": "Sons and Lovers"}, {"query_id": "253", "query": "In novel Winesburg, Ohio, has the word or phrase rambling appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the word \"rambling\" appears once in the novel \"Winesburg, Ohio.\" It is used in a passage where a character's voice becomes soft and reminiscent, and he begins a long, rambling talk, speaking as if lost in a dream.", "novel": "Winesburg, Ohio"}, {"query_id": "254", "query": "In novel Winesburg, Ohio, has the object guinea hen appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, the object guinea hen has appeared in the novel Winesburg, Ohio. It appears once in the text, specifically in the scene where a flock of chickens, accompanied by two guinea hens, lay in the deep dust in the road before the house where <PERSON><PERSON> lived with his mother during his youth.", "novel": "Winesburg, Ohio"}, {"query_id": "255", "query": "Has the place Oregon appeared? If so, how many times does it appear in the text?", "gt_answer": "Yes, Oregon has appeared in the text, and it is mentioned once when the character recalls planning to go west to his uncle in Portland, Oregon.", "novel": "Winesburg, Ohio"}, {"query_id": "256", "query": "In novel Winesburg, Ohio, in which countrie does this story take place?", "gt_answer": "The story takes place in America, specifically in the town of Winesburg, Ohio, where a small frame house near the edge of a ravine is described, and a fat little old man is seen walking nervously up and down the half-decayed veranda.", "novel": "Winesburg, Ohio"}, {"query_id": "257", "query": "Has the character <PERSON> appeared? If so, how many times does he/she/they appear in the text?", "gt_answer": "Yes, <PERSON> has appeared in the text, and he appears once. He is described as a middle-aged man who works as a clerk in a drug store and is also a member of the church.", "novel": "Winesburg, Ohio"}, {"query_id": "258", "query": "Has the character <PERSON> appeared? If so, how many times does he/she/they appear in the text?", "gt_answer": "Yes, <PERSON> has appeared in the text, and he appears once.", "novel": "Winesburg, Ohio"}, {"query_id": "259", "query": "How many times has <PERSON>  picked up the corn?", "gt_answer": "<PERSON> has picked up the corn once.", "novel": "Winesburg, Ohio"}, {"query_id": "260", "query": "What is the relationship between <PERSON> and <PERSON>?", "gt_answer": "<PERSON> is <PERSON>'s stepfather, having married her mother when <PERSON> was twenty-five years old.", "novel": "Winesburg, Ohio"}, {"query_id": "261", "query": "What is the relationship between <PERSON> and the nursery man?", "gt_answer": "<PERSON> and the nursery man are the same person. For ten years, <PERSON>, who owned a tree nursery, was the only friend of Doctor <PERSON>. Sometimes, Doctor <PERSON> would playfully throw paper balls at <PERSON>, calling him a blathering old sentimentalist and laughing heartily.", "novel": "Winesburg, Ohio"}, {"query_id": "262", "query": "How many times have <PERSON> and <PERSON><PERSON> communicated with each other?", "gt_answer": "<PERSON> and <PERSON><PERSON> have communicated with each other once.", "novel": "Winesburg, Ohio"}, {"query_id": "263", "query": "In novel Winesburg, Ohio, in which chapter does there exist a sentence with the same or similar meaning as 'The Banker White built a brick mansion on Buckeye Street, which grasps others attention'?", "gt_answer": "Chapter <The Thinker> contains a sentence with a similar meaning to \"The Banker White built a brick mansion on Buckeye Street, which grasps others attention,\" as it mentions that the huge brick house which <PERSON><PERSON> had built on Buckeye Street had overshadowed it.", "novel": "Winesburg, Ohio"}, {"query_id": "264", "query": "In novel Winesburg, Ohio, in which chapter does there exist a sentence with the same or similar meaning as 'He was dejected because he didn't feel a sense of belonging in the town, however he was not so depressed because he didn't think it's his fault.'?", "gt_answer": "Chapter <The Thinker>, where the sentence \"He was depressed by the thought that he was not a part of the life in his own town, but the depression did not cut deeply as he did not think of himself as at fault.\" appears, conveys a similar meaning to \"He was dejected because he didn't feel a sense of belonging in the town, however he was not so depressed because he didn't think it's his fault.\"", "novel": "Winesburg, Ohio"}, {"query_id": "265", "query": "How many times have <PERSON> and Mr. <PERSON><PERSON> l moved into the church?", "gt_answer": "<PERSON> and Mr. <PERSON><PERSON> have moved into the church together once, with Mr. <PERSON><PERSON> carrying a vast bundle of ivy and fern, while the rector and his sister were laden with closely-packed baskets of cut flowers.", "novel": "<PERSON><PERSON>"}, {"query_id": "266", "query": "Who is Mr<PERSON> <PERSON><PERSON><PERSON><PERSON>?", "gt_answer": "Mr. <PERSON><PERSON><PERSON><PERSON>, who is the Member of Parliament for West Brookshire, married Lady <PERSON><PERSON>, the only surviving daughter of Lord <PERSON>.", "novel": "<PERSON><PERSON>"}, {"query_id": "267", "query": "In novel <PERSON><PERSON>, in which chapter does there exist a sentence with the same or similar meaning as 'As <PERSON><PERSON> sat leaning forward in the gallery of the Widrington Assize Court, the solemn words resonated through her ears, 'May the Lord have mercy on your soul!''?", "gt_answer": "Chapter 13, where the deep-pitched words \"And may the Lord have mercy on your soul!\" fell slowly on <PERSON><PERSON>'s ears as she sat leaning forward in the gallery of the Widrington Assize Court.", "novel": "<PERSON><PERSON>"}, {"query_id": "268", "query": "What is the relationship between Mr<PERSON> <PERSON><PERSON><PERSON><PERSON> and Lady <PERSON>?", "gt_answer": "Mr. <PERSON><PERSON><PERSON><PERSON> was the husband of Lady <PERSON><PERSON>, who was the only surviving daughter of Lord <PERSON>. Mr. <PERSON><PERSON><PERSON><PERSON> was also the Member of Parliament for West Brookshire.", "novel": "<PERSON><PERSON>"}, {"query_id": "269", "query": "How many times has <PERSON> Jumped?", "gt_answer": "<PERSON> has jumped once.", "novel": "<PERSON><PERSON>"}, {"query_id": "270", "query": "With whom <PERSON> has spent one year in the Oxford college, in <PERSON>'s first youth?", "gt_answer": "<PERSON> spent one year in the Oxford college with <PERSON> during her first youth, when he was doing some divinity lecturing there.", "novel": "<PERSON><PERSON>"}, {"query_id": "271", "query": "In novel <PERSON><PERSON>, who pondered the matter a good deal when he or she was left alone?", "gt_answer": "Mrs. <PERSON><PERSON> pondered the matter a good deal when she was left alone.", "novel": "<PERSON><PERSON>"}, {"query_id": "272", "query": "In Chapter IX of the novel <PERSON><PERSON>, Mrs. <PERSON> once flew to the door of their house, when a short, deformed, and muddy man stumbled in blindly. What color was the hair of the man walking in?", "gt_answer": "The man who stumbled blindly into the house, described as short and deformed, had red hair.", "novel": "<PERSON><PERSON>"}, {"query_id": "273", "query": "How old was <PERSON><PERSON> when she had first started her school?", "gt_answer": "<PERSON><PERSON> was nine years old when she had first started her school.", "novel": "<PERSON><PERSON>"}, {"query_id": "274", "query": "at what age had <PERSON><PERSON> perfectly understood that she was one of the Boyces of Brookshire, and that her great-uncle had been a famous Speaker of the House of Commons?", "gt_answer": "<PERSON><PERSON> perfectly understood that she was one of the Boy<PERSON> of Brookshire, and that her great-uncle had been a famous Speaker of the House of Commons, when she was ten years old.", "novel": "<PERSON><PERSON>"}, {"query_id": "275", "query": "When <PERSON> had gone, <PERSON><PERSON> went to the fire and put it together, sighing all the time, her face still which color and miserable?", "gt_answer": "<PERSON><PERSON>'s face remained red and miserable as she went to the fire and put it together, sighing all the time after <PERSON><PERSON> had gone.", "novel": "<PERSON><PERSON>"}, {"query_id": "276", "query": "When <PERSON><PERSON><PERSON> came in room who smiled and lifted a feeble hand towards the park and the woods?", "gt_answer": "<PERSON><PERSON> smiled and lifted a feeble hand towards the park and the woods when <PERSON><PERSON><PERSON> came into the room.", "novel": "<PERSON><PERSON>"}, {"query_id": "277", "query": "How many times have <PERSON> and <PERSON><PERSON> arranged the shabby furniture?", "gt_answer": "<PERSON> and <PERSON><PERSON> have arranged the shabby furniture once.", "novel": "Lover or Friend"}, {"query_id": "278", "query": "In novel Lover or Friend, in which chapter does there exist a sentence with the same or similar meaning as 'She was dissatisfied with the conversation's trajectory; She didn't like ideas that were too abstract; Her excessive boredom was caused by <PERSON>'s wordplay.'?", "gt_answer": "Chapter 1 contains a sentence that conveys the same or similar meaning as 'She was dissatisfied with the conversation's trajectory; She didn't like ideas that were too abstract; Her excessive boredom was caused by <PERSON>'s wordplay.'", "novel": "Lover or Friend"}, {"query_id": "279", "query": "In novel Lover or Friend, in which chapter does there exist a sentence with the same or similar meaning as 'What a gift that new cambric is done! <PERSON> will be so satisfied when I educate him regarding your generosity.'?", "gt_answer": "Chapter 13 contains a sentence with a similar meaning to \"What a gift that new cambric is done! <PERSON> will be so satisfied when I educate him regarding your generosity.\"", "novel": "Lover or Friend"}, {"query_id": "280", "query": "In novel Lover or Friend, has someone ever lost their daughter? If so, how many times has this plot happened?", "gt_answer": "Yes, in the novel \"Lover or Friend,\" someone has lost their daughter. Mrs. <PERSON> still regretted the loss of her elder daughter, <PERSON><PERSON>, and complained that no one could replace her. This plot has happened once.", "novel": "Lover or Friend"}, {"query_id": "281", "query": "In novel <PERSON> or Friend, in which chapter does there exist a sentence with the same or similar meaning as '<PERSON> <PERSON><PERSON> was devastated by the thought of leaving her friend, and <PERSON> had to wait a long time to get her to see things in a less gloomy light.'?", "gt_answer": "Chapter 44 contains a sentence with the same or similar meaning as \"Poor <PERSON><PERSON> was devastated by the thought of leaving her friend, and <PERSON> had to wait a long time to get her to see things in a less gloomy light.\"", "novel": "Lover or Friend"}, {"query_id": "282", "query": "Even when <PERSON><PERSON> was in the which place, her energy and youthful vigor began to assert themselves, her opinions insensibly influenced her mother's, until at last they swayed her entirely?", "gt_answer": "Even when <PERSON><PERSON> was in the schoolroom, her energy and youthful vigor began to assert themselves, and her opinions insensibly influenced her mother's until at last, they swayed her entirely.", "novel": "Lover or Friend"}, {"query_id": "283", "query": "With which place <PERSON> has happiest associations?", "gt_answer": "<PERSON> has the happiest associations with <PERSON> because she was only a little girl when her father came to Woodcote, and all her happiest memories are tied to <PERSON>.", "novel": "Lover or Friend"}, {"query_id": "284", "query": "Who was more contradictory when <PERSON> was in the background?", "gt_answer": "<PERSON> was always more contradictory when <PERSON> was in the background.", "novel": "Lover or Friend"}, {"query_id": "285", "query": "When <PERSON> returned what was in its place, and <PERSON> was lying with his eyes closed, and the frown of pain still knitting his temples?", "gt_answer": "When <PERSON> returned, the book was in its place, and <PERSON> was lying with his eyes closed, with the frown of pain still knitting his temples.", "novel": "Lover or Friend"}, {"query_id": "286", "query": "When <PERSON> entered the drawing-oom she found her who standing in his favorite attitude before the fireplace?", "gt_answer": "<PERSON> entered the drawing-room and found her brother-in-law standing in his favorite attitude before the fireplace. He was evidently holding forth on some interesting topic, as Dr<PERSON> <PERSON> was listening to him with an amused expression, and <PERSON><PERSON> was watching him with admiring wifely eyes.", "novel": "Lover or Friend"}, {"query_id": "287", "query": "Mrs. <PERSON>, on the contrary, had a long melancholy face and which color of eyes?", "gt_answer": "Mrs. <PERSON>, on the contrary, had a long melancholy face and anxious blue eyes.", "novel": "Lover or Friend"}, {"query_id": "288", "query": "brother-in-law standing of <PERSON> in his favourite attitude before the fireplace...he was evidently holding forth on some interesting topic, for whom listening to him with an amused expression of face, and <PERSON><PERSON> was watching him with admiring wifely eyes?", "gt_answer": "Dr. <PERSON> was listening to <PERSON>'s brother-in-law with an amused expression on his face, while <PERSON><PERSON> was watching him with admiring wifely eyes.", "novel": "Lover or Friend"}, {"query_id": "289", "query": "In novel White Fang, what was on the sled at the beginning of the story?", "gt_answer": "At the beginning of the story in the novel <PERSON> Fang, the sled carried blankets, an axe, a coffee-pot, a frying-pan, and a long and narrow oblong box, which was securely lashed and occupied most of the space on the sled.", "novel": "White Fang"}, {"query_id": "290", "query": "How many cartridges did <PERSON> had left in chapter one?", "gt_answer": "<PERSON> had three cartridges left in chapter one.", "novel": "White Fang"}, {"query_id": "291", "query": "In novel White Fang, what's the color of the she-wolf?", "gt_answer": "The color of the she-wolf in the novel \"White Fang\" is between red and grey. Her coat is primarily grey, but it has a faint reddish hue that appears and disappears, making it seem like an illusion. This reddish tint is subtle and not easily classified, giving her a unique coloration that shifts between grey and hints of red.", "novel": "White Fang"}, {"query_id": "292", "query": "What happened to <PERSON>?", "gt_answer": "<PERSON> was killed by the wolf. Somewhere out there in the snow, screened from <PERSON>'s sight by trees and thickets, the wolf-pack, <PERSON>, and <PERSON> came together. <PERSON> heard a shot, then two shots in rapid succession, indicating that <PERSON>'s ammunition was gone. This was followed by a great outcry of snarls and yelps, including <PERSON> <PERSON><PERSON>'s yell of pain and terror, and a wolf-cry that indicated a stricken animal. After that, the snarls ceased, the yelping died away, and silence settled over the lonely land.", "novel": "White Fang"}, {"query_id": "293", "query": "What were <PERSON> and <PERSON> heading for?", "gt_answer": "<PERSON> and <PERSON> were heading for Fort McGurry, which they believed to be a place of safety. The dogs pulling their sled also seemed to understand that reaching Fort McGurry was crucial for their safety.", "novel": "White Fang"}, {"query_id": "294", "query": "What prey did One Eye hunt in Part II Chapter II?", "gt_answer": "One Eye hunted a ptarmigan and a porcupine. He first stretched out the porcupine to its full length, turned it over on its back, and confirmed it was dead. He then carefully gripped it with his teeth and started carrying and dragging it down the stream, avoiding the prickly parts. Remembering the ptarmigan he had left behind, he dropped the porcupine, went back, and promptly ate the ptarmigan before returning to retrieve the porcupine.", "novel": "White Fang"}, {"query_id": "295", "query": "What did One Eye do to hunt for food in the first days after the birth of the cubs?", "gt_answer": "One <PERSON> journeyed several times back to the Indian camp and robbed the rabbit snares in the first days after the birth of the cubs. However, with the melting of the snow and the opening of the streams, the Indian camp moved away, closing that source of supply to him.", "novel": "White Fang"}, {"query_id": "296", "query": "Why did One Eye disappear?", "gt_answer": "One Eye lost the battle with the lynx and died. The grey cub no longer saw his father appearing and disappearing in the wall or lying down asleep in the entrance. This disappearance occurred at the end of a second and less severe famine. The she-wolf knew why <PERSON> Eye never came back, but she couldn't communicate this to the grey cub. While hunting for meat near the lynx's territory, she followed a day-old trail of <PERSON> Eye and found what remained of him at the end of the trail. The signs of the battle and the lynx's retreat to her lair indicated that <PERSON> Eye had been defeated and killed by the lynx.", "novel": "White Fang"}, {"query_id": "297", "query": "Who was <PERSON><PERSON>'s original owner?", "gt_answer": "<PERSON><PERSON>'s original owner was <PERSON> Beaver's brother, who is now deceased.", "novel": "White Fang"}, {"query_id": "298", "query": "Why did <PERSON> never play with other puppies of the camp?", "gt_answer": "<PERSON> never played with other puppies of the camp because every time he appeared near them, <PERSON><PERSON><PERSON><PERSON> would bully him and drive him away. <PERSON><PERSON><PERSON><PERSON> would not permit <PERSON> to play and gambol about with the other puppies, as he would immediately start bullying, hectoring, or fighting with <PERSON> until he was forced to leave.", "novel": "White Fang"}, {"query_id": "299", "query": "How did Beauty manage to buy White Fang from Grey Beaver?", "gt_answer": "<PERSON> used alcohol to con <PERSON> into selling White Fang. <PERSON> frequently visited <PERSON> Beaver's camp, always bringing bottles of whisky. The whisky created a powerful thirst in Grey Beaver, causing him to crave more and more of the alcohol. As <PERSON>'s thirst grew, he spent all his money and goods on whisky, leaving him with nothing but an overwhelming desire for more. Eventually, <PERSON> offered to buy White Fang in exchange for bottles of whisky instead of money, and <PERSON> Beaver, driven by his intense thirst, agreed to the deal.", "novel": "White Fang"}], "gt_answer_claims": [[["Ra<PERSON><PERSON><PERSON>v", "owes", "money"], ["Ra<PERSON><PERSON><PERSON>v", "is", "in debt"], ["Ra<PERSON><PERSON><PERSON>v", "is afraid of", "encountering his landlady"], ["Ra<PERSON><PERSON><PERSON>v", "reason for not wanting to meet his landlady", "owing money"], ["Ra<PERSON><PERSON><PERSON>v", "reason for not wanting to meet his landlady", "being in debt"]], [["Ra<PERSON><PERSON><PERSON>v", "has ulterior motive for", "visiting the old widow's house"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>'s ulterior motive", "is", "to rehearse the murder"], ["Ra<PERSON><PERSON><PERSON>v", "went to", "the old widow's house"], ["Ra<PERSON><PERSON><PERSON>v", "went for", "a rehearsal of his project"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>'s excitement", "grew", "more and more violent"]], [["Ra<PERSON><PERSON><PERSON>v", "feels", "disgusted with himself"], ["Ra<PERSON><PERSON><PERSON>v", "thinks", "his heart is capable of filthy, loathsome things"], ["Ra<PERSON><PERSON><PERSON>v", "is appalled by", "atrocious thought"], ["atrocious thought", "enters", "his mind"], ["Ra<PERSON><PERSON><PERSON>v", "is deeply disturbed by", "realization of his own capacity for such thoughts"]], [["Ra<PERSON><PERSON><PERSON>v", "meets", "<PERSON><PERSON><PERSON><PERSON>"], ["Ra<PERSON><PERSON><PERSON>v", "meets in", "the tavern"], ["<PERSON><PERSON><PERSON><PERSON>", "introduces himself as", "a titular counsellor"], ["<PERSON><PERSON><PERSON><PERSON>", "engages in", "conversation"], ["<PERSON><PERSON><PERSON><PERSON>", "respects", "education"], ["<PERSON><PERSON><PERSON><PERSON>", "respects", "genuine sentiments"]], [["Luzhin", "wants to marry", "<PERSON><PERSON><PERSON>"], ["Luzhin", "believes", "a man should not be indebted to his wife"], ["Luzhin", "believes", "it is better for a wife to view her husband as her benefactor"], ["Luzhin", "decided to marry", "a girl of good reputation"], ["Luzhin", "decided to marry", "a girl without a dowry"], ["Luzhin", "decided to marry", "a girl who had experienced poverty"], ["<PERSON><PERSON><PERSON>'s desire", "is for", "<PERSON><PERSON><PERSON> to always look upon him as her benefactor"]], [["Ra<PERSON><PERSON><PERSON>v", "decides", "he will not let his sister marry <PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>'s mother's letter", "was", "a torture to him"], ["Ra<PERSON><PERSON><PERSON>v", "felt", "no hesitation"], ["Ra<PERSON><PERSON><PERSON>v", "settled", "the essential question irrevocably in his mind"], ["Ra<PERSON><PERSON><PERSON>v", "will never allow", "such a marriage while he was alive"], ["Ra<PERSON><PERSON><PERSON>v", "damned", "Mr. <PERSON><PERSON>"]], [["The student in the bar", "expressed", "idea"], ["The idea", "is that", "humanity would be better off if <PERSON><PERSON><PERSON> were dead"], ["The idea", "suggests", "killing her and taking her money could be justified"], ["Justification", "is that", "the money were then used to serve humanity and the greater good"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>'s own thoughts", "coincided with", "The idea"]], [["Ra<PERSON><PERSON><PERSON>v", "kills", "<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "location of death", "<PERSON><PERSON><PERSON>'s apartment"], ["Ra<PERSON><PERSON><PERSON>v", "kills", "<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "characterized as", "simple"], ["<PERSON><PERSON><PERSON>", "characterized as", "thoroughly crushed"], ["<PERSON><PERSON><PERSON>", "characterized as", "scared"], ["<PERSON><PERSON><PERSON>", "action", "did not even raise a hand to guard her face"], ["axe", "action", "was raised over her"]], [["Ra<PERSON><PERSON><PERSON>v", "thinks", "he's being punished for the murder already"], ["Ra<PERSON><PERSON><PERSON>v", "has", "hard time concentrating"], ["Ra<PERSON><PERSON><PERSON>v", "feels", "all his faculties are failing him"], ["Ra<PERSON><PERSON><PERSON>v", "feels", "insufferable torture"], ["Ra<PERSON><PERSON><PERSON>v", "is convinced", "his punishment is already beginning"], ["Raskolnikov's faculties", "include", "memory"], ["Raskolnikov's faculties", "include", "power of reflection"]], [["Ra<PERSON><PERSON><PERSON>v", "called to", "police station"], ["Ra<PERSON><PERSON><PERSON>v", "owes", "landlady"], ["Ra<PERSON><PERSON><PERSON>v", "summoned for", "recovery of money"], ["IOU", "amounts to", "a hundred and fifteen roubles"], ["IOU", "given by", "Ra<PERSON><PERSON><PERSON>v"], ["IOU", "given to", "widow of the assessor <PERSON><PERSON><PERSON><PERSON><PERSON>"], ["IOU", "given", "nine months ago"], ["IOU", "paid over to", "Mr. <PERSON><PERSON><PERSON><PERSON>"], ["Police", "require", "<PERSON><PERSON><PERSON><PERSON>v to pay the debt"], ["Police", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON> to provide written declaration"], ["Police", "require", "Rasko<PERSON>ikov not to leave the capital"], ["Police", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON> not to conceal his property"]], [["Ra<PERSON><PERSON><PERSON>v", "buried", "his tracks"], ["Ra<PERSON><PERSON><PERSON>v", "turned over", "a stone"], ["Ra<PERSON><PERSON><PERSON>v", "created", "a small hollow in the ground"], ["Ra<PERSON><PERSON><PERSON>v", "emptied", "his pocket"], ["Ra<PERSON><PERSON><PERSON>v", "placed", "the purse"], ["the purse", "location", "at the top"], ["Ra<PERSON><PERSON><PERSON>v", "turned", "the stone"], ["Ra<PERSON><PERSON><PERSON>v", "ensured", "it looked undisturbed"], ["Ra<PERSON><PERSON><PERSON>v", "scraped", "the earth"], ["Ra<PERSON><PERSON><PERSON>v", "pressed", "the edges"], ["the edges", "with", "his foot"]], [["<PERSON><PERSON><PERSON><PERSON>", "is lying in", "street"], ["<PERSON><PERSON><PERSON><PERSON>", "reason for lying in street", "being drunk"], ["<PERSON><PERSON><PERSON><PERSON>", "has been", "run over by a horse"], ["<PERSON><PERSON><PERSON><PERSON>", "action before being run over", "thrown himself under the horses"], ["<PERSON><PERSON><PERSON><PERSON>", "state when throwing himself", "intoxicated"]], [["Ra<PERSON><PERSON><PERSON>v", "orders", "<PERSON><PERSON><PERSON>"], ["Ra<PERSON><PERSON><PERSON>v", "does not want", "the marriage to happen"], ["Ra<PERSON><PERSON><PERSON>v", "instructs", "<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "to do", "refuse Luzhin"], ["<PERSON><PERSON><PERSON>", "to do", "end her engagement with <PERSON><PERSON><PERSON>"], ["Refusal of Luzhin", "to occur at", "the first opportunity the next day"], ["Ra<PERSON><PERSON><PERSON>v", "wants", "they never hear <PERSON><PERSON><PERSON>'s name again"]], [["<PERSON>", "interrupts", "conversation between <PERSON><PERSON><PERSON><PERSON><PERSON>, his family, and <PERSON><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON>", "arrives", "falteringly"], ["<PERSON>", "apologizes for", "disturbing them"], ["<PERSON>", "explains", "that she has come on behalf of <PERSON><PERSON>"], ["<PERSON>", "conveys", "<PERSON><PERSON>'s request"], ["<PERSON><PERSON>'s request", "is for them to attend", "service in the morning at Mitrofanievsky"], ["<PERSON><PERSON>'s request", "is for them to", "visit her"]], [["Porfiry", "asks", "Ra<PERSON><PERSON><PERSON>v"], ["Porfiry", "inquires about", "the painters near <PERSON><PERSON><PERSON>'s apartment"], ["Porfiry", "attempts to", "<PERSON> <PERSON><PERSON><PERSON>"], ["Ra<PERSON><PERSON><PERSON>v", "denies", "seeing any painters"], ["Ra<PERSON><PERSON><PERSON>v", "tries to recall", "details about the flat opposite <PERSON><PERSON><PERSON>'s"], ["Ra<PERSON><PERSON><PERSON>v", "mentions", "porters moving a sofa"], ["<PERSON><PERSON><PERSON><PERSON>", "points out", "the painters were working on the day of the murder"], ["Ra<PERSON><PERSON><PERSON>v", "was at <PERSON><PERSON><PERSON>'s apartment", "three days before the murder"], ["Porfiry", "realizes", "his mistake"], ["Porfiry", "apologizes for", "the confusion"]], [["The stranger in the street", "says", "Murderer!"], ["The stranger in the street", "speaks in", "a quiet but clear and distinct voice"], ["Ra<PERSON><PERSON><PERSON>v", "continues", "walking"], ["Ra<PERSON><PERSON><PERSON>v", "feels", "his legs suddenly weaken"], ["Ra<PERSON><PERSON><PERSON>v", "feels", "a cold shiver running down his spine"], ["Ra<PERSON><PERSON><PERSON>v", "feels", "his heart seeming to stand still for a moment"], ["Ra<PERSON><PERSON><PERSON>v", "feels", "his heart throb as though it were set free"], ["<PERSON><PERSON><PERSON><PERSON><PERSON> and the stranger in the street", "walk", "for about a hundred paces"], ["<PERSON><PERSON><PERSON><PERSON><PERSON> and the stranger in the street", "walk", "side by side in silence"]], [["<PERSON>", "believes", "God"], ["God", "will take care of", "her family"], ["<PERSON>", "is in", "a state of desperation"], ["<PERSON>", "expresses", "her faith"], ["<PERSON>'s faith", "is that", "God would not allow anything terrible to happen to them"], ["<PERSON>", "clings to", "the hope"], ["the hope", "is that", "God will protect her family from harm"]], [["Ra<PERSON><PERSON><PERSON>v", "has resolved to", "separate himself from his family"], ["Ra<PERSON><PERSON><PERSON>v", "has abandoned", "his mother"], ["Ra<PERSON><PERSON><PERSON>v", "has abandoned", "his sister"], ["Ra<PERSON><PERSON><PERSON>v", "decided not to", "see them anymore"], ["Ra<PERSON><PERSON><PERSON>v", "broken", "all ties with them completely"]], [["Mr. <PERSON><PERSON><PERSON><PERSON><PERSON>", "eavesdrops on", "<PERSON><PERSON><PERSON><PERSON><PERSON>'s conversation with <PERSON>"], ["Mr. <PERSON><PERSON><PERSON><PERSON><PERSON>", "stands", "at the door"], ["The door", "of", "the empty room"], ["The empty room", "adjacent to", "<PERSON>'s room"], ["The empty room", "status", "uninhabited"]], [["Luzhin", "believes", "if he had spent around fifteen hundred roubles on trousseau and presents, his position would have been better and stronger"], ["Luzhin", "would have spent", "fifteen hundred roubles"], ["Fifteen hundred roubles", "used for", "trousseau and presents"], ["<PERSON><PERSON><PERSON> and presents", "include", "knick-knacks"], ["<PERSON><PERSON><PERSON> and presents", "include", "dressing-cases"], ["<PERSON><PERSON><PERSON> and presents", "include", "jewelry"], ["<PERSON><PERSON><PERSON> and presents", "include", "materials"], ["<PERSON><PERSON><PERSON> and presents", "include", "other items"], ["Other items", "from", "<PERSON><PERSON><PERSON>'s"], ["Other items", "from", "the English shop"], ["<PERSON><PERSON><PERSON>'s position", "would have been", "better and stronger"], ["<PERSON><PERSON><PERSON>'s position", "would have made", "it more difficult for <PERSON><PERSON><PERSON> to refuse him so easily"], ["<PERSON><PERSON><PERSON>", "might have been refused", "by <PERSON><PERSON><PERSON>"]], [["workhouse authorities", "offer", "reward of five pounds"], ["workhouse authorities", "offer", "<PERSON>"], ["reward of five pounds and <PERSON>", "offered to", "any man or woman"], ["any man or woman", "wanted", "an apprentice"], ["an apprentice", "for", "any trade, business, or calling"]], [["Mr. <PERSON>", "is", "chimney-sweep"], ["Mr. <PERSON>", "was doing", "thinking"], ["Thinking of Mr<PERSON>", "about", "how to pay his overdue rent"], ["Mr. <PERSON>", "has", "landlord"], ["Landlord of Mr<PERSON>", "became", "insistent"], ["Mr. <PERSON>", "was walking", "down the High Street"], ["Walking of Mr<PERSON>", "occurred in the", "morning"]], [["Mrs. <PERSON><PERSON><PERSON>", "is concerned about", "<PERSON>'s large appetite"], ["<PERSON>", "has", "large appetite"], ["<PERSON>", "is", "good-looking boy"], ["Mrs. <PERSON><PERSON><PERSON>", "remarks about", "<PERSON>'s food consumption"], ["<PERSON>'s food consumption", "amount", "a lot"], ["Mrs. <PERSON><PERSON><PERSON>", "feeds", "<PERSON>"], ["<PERSON>", "has meal", "at Mrs. <PERSON><PERSON><PERSON>'s home"], ["<PERSON>'s meal", "is", "first meal"]], [["deceased", "cause of death", "starvation"], ["person who found her", "swore", "before God"], ["deceased", "state when found", "dying"], ["deceased", "killed by", "starvation"]], [["<PERSON>", "gains extensive experience in", "undertaking"], ["Undertaking", "after", "many people die"], ["Many people", "die in", "epidemic"], ["Epidemic", "of", "measles"], ["<PERSON><PERSON><PERSON>", "had been", "so widespread"], ["<PERSON><PERSON><PERSON>", "had been", "so deadly to infants"], ["Infants", "leading to", "numerous mournful processions"], ["<PERSON>", "led", "mournful processions"], ["<PERSON>", "wearing", "hat-band"], ["Hat-band", "reached down to", "his knees"], ["Mothers in the town", "admiration and emotion of", "<PERSON>"]], [["<PERSON>", "decides to walk to", "London"], ["<PERSON>", "flees", "<PERSON><PERSON><PERSON>'s home"], ["London", "distance from <PERSON><PERSON><PERSON>'s home", "seventy miles"]], [["<PERSON><PERSON><PERSON>", "threatens", "<PERSON>"], ["<PERSON><PERSON><PERSON>", "uses", "bread knife"], ["<PERSON><PERSON><PERSON>", "grabs", "bread knife"], ["<PERSON><PERSON><PERSON>", "holds up", "bread knife"], ["bread knife", "is from", "table"], ["bread knife", "quivers", "in the air"]], [["<PERSON>", "sentenced to", "three months of hard labor"], ["<PERSON>", "crime", "picking Mr. <PERSON>'s pocket"], ["Sentence of Oliver", "duration", "three months"], ["Sentence of Oliver", "type", "hard labor"]], [["<PERSON>", "awakes from", "fever"], ["<PERSON>", "at", "<PERSON><PERSON>'s house"], ["<PERSON>", "sitting beside", "an old lady"], ["an old lady", "telling", "<PERSON>"], ["an old lady", "advising", "be very quiet"], ["an old lady", "mentioning", "Oliver has been very bad"], ["an old lady", "encouraging", "<PERSON> to lie down again"]], [["Nancy", "goes to", "jail"], ["Nancy", "asks after", "<PERSON>"], ["Nancy", "enters", "jail"], ["Nancy", "enters by", "back way"], ["Nancy", "taps", "cell door"], ["Nancy", "uses", "key"], ["Nancy", "listens", "within"], ["Nancy", "calls out", "Nolly, dear?"], ["Nancy", "calls out", "Nolly?"]], [["Mr. <PERSON><PERSON>", "answers", "Mr. <PERSON>'s ad"], ["Mr. <PERSON>'s ad", "offers", "reward for information on <PERSON>"], ["Mr. <PERSON><PERSON>", "reads", "the advertisement"], ["Mr. <PERSON><PERSON>", "becomes", "excited"], ["Mr. <PERSON><PERSON>", "heads to", "Pentonville"], ["Mr. <PERSON><PERSON>", "leaves", "his glass of hot gin-and-water"], ["Mr. <PERSON><PERSON>", "inquires", "if Mr. <PERSON> is at home"], ["Mr. <PERSON><PERSON>", "arrives at", "Pentonville"]], [["<PERSON>", "shows", "pistol"], ["<PERSON>", "threatens", "<PERSON>"], ["<PERSON>", "has", "pistol"], ["pistol", "located in", "side-pocket of his great-coat"], ["<PERSON>", "makes", "menacing gesture"], ["menacing gesture", "indicates", "seriousness of his threat"]], [["Old Sally", "admits to", "robbing <PERSON>'s mother"], ["<PERSON>'s mother", "died", "on her deathbed"], ["<PERSON>'s mother", "whispered to", "Old Sally"], ["<PERSON>'s mother", "expressed hope for", "her baby"], ["<PERSON>'s mother", "prayed for", "her child"], ["Old Sally", "stole", "gold"], ["Old Sally", "stole from", "the dying woman"], ["The dying woman", "is", "<PERSON>'s mother"]], [["Brittles", "opened", "the door"], ["Brittles", "behind", "the door"], ["Brittles", "saw", "<PERSON>"], ["<PERSON>", "was", "gravely wounded"], ["Brittles", "uttered", "a loud cry"], ["Mr. <PERSON>", "seized", "<PERSON>"], ["Mr. <PERSON>", "seized", "one leg"], ["Mr. <PERSON>", "seized", "one arm"], ["Mr. <PERSON>", "lugged", "<PERSON>"], ["Mr. <PERSON>", "deposited", "<PERSON>"], ["<PERSON>", "was deposited", "at full length on the floor"], ["<PERSON>", "was in", "the hall"]], [["Mr. <PERSON>", "is", "local surgeon"], ["Mr. <PERSON>", "also known as", "the doctor"], ["Mr. <PERSON>", "has characteristic", "eccentric"], ["Mr. <PERSON>", "has characteristic", "old bachelor"], ["Mr. <PERSON>", "has characteristic", "fat"], ["Mr. <PERSON>", "has characteristic", "good humor"], ["Mr. <PERSON>", "has characteristic", "kind"], ["Mr. <PERSON>", "has characteristic", "hearty"]], [["Mr. <PERSON> and <PERSON>", "learn", "<PERSON><PERSON> and Mrs. <PERSON><PERSON> have moved to the West Indies"], ["<PERSON><PERSON> and Mrs. <PERSON><PERSON>", "moved to", "the West Indies"], ["<PERSON>", "feeling", "great distress"], ["Mr. <PERSON>", "sold", "his goods"], ["Mr. <PERSON>", "left for", "the West Indies"], ["Mr. <PERSON>'s departure", "occurred", "six weeks prior"], ["<PERSON>", "action", "clasp his hands and sink feebly backward in despair"]], [["Monks", "is", "<PERSON>'s brother"], ["Nancy", "speaks to", "<PERSON>"], ["Nancy", "reveals", "<PERSON><PERSON> is <PERSON>'s brother"], ["Monks", "harbors", "deep hatred for <PERSON>"], ["Monks", "would go to", "great lengths to harm <PERSON>"], ["Monks", "would take", "<PERSON>'s life"], ["Monks", "would take <PERSON>'s life", "if he could do so without endangering himself"], ["Monks", "plans to be", "constant threat to <PERSON>"], ["Monks", "uses", "<PERSON>'s birth and history"], ["Monks", "uses <PERSON>'s birth and history", "against him"], ["<PERSON>", "is shocked to learn", "<PERSON><PERSON> is <PERSON>'s brother"]], [["Mr. <PERSON>", "full name", "<PERSON>"], ["<PERSON>", "revealed to be", "<PERSON>'s biological father"], ["<PERSON>", "had a relationship with", "<PERSON>"], ["<PERSON>", "died", "while giving birth to <PERSON>"], ["<PERSON>", "biological father", "<PERSON>"]], [["<PERSON>", "chooses to pursue", "occupation of a clergyman"], ["<PERSON>", "leads to", "<PERSON> to agree to marry him"], ["<PERSON>", "agrees to marry", "<PERSON>"], ["<PERSON>", "married to", "<PERSON>"], ["Marriage of <PERSON> and <PERSON>", "occurred in", "village church"], ["Village church", "becomes", "scene of the young clergyman's labours"], ["<PERSON>", "becomes", "young clergyman"], ["<PERSON> and <PERSON>", "enter into possession of", "new and happy home"], ["New and happy home", "obtained on", "same day"]], [["<PERSON>", "is", "member of <PERSON><PERSON><PERSON>'s gang"], ["<PERSON>", "testifies against", "<PERSON><PERSON><PERSON>"], ["<PERSON>", "is pardoned by", "the Crown"], ["<PERSON>", "receives", "free pardon"], ["<PERSON>", "needs", "new means of livelihood"], ["<PERSON>", "considers", "previous profession unsafe"], ["<PERSON>", "becomes", "Informer"], ["<PERSON>", "earns", "respectable living"]], [["<PERSON><PERSON><PERSON>", "is", "Jewish"], ["<PERSON><PERSON><PERSON>", "referred to as", "the Jew"], ["the Jew", "refers to", "<PERSON><PERSON><PERSON>"]], [["Mansfield Park", "contains", "vulnerable"], ["vulnerable", "frequency in Mansfield Park", "once"]], [["Mansfield Park", "contains", "hectic"], ["hectic", "frequency of appearance in Mansfield Park", "once"]], [["<PERSON><PERSON><PERSON>", "has appeared", "yes"], ["<PERSON><PERSON><PERSON>", "number of appearances", "once"]], [["the Canopus", "has appeared", "yes"], ["the Canopus", "number of appearances", "once"]], [["Garrison chapel", "has appeared", "in the text"], ["Garrison chapel", "number of appearances", "once"], ["Garrison chapel", "visited by", "he"], ["he", "went to", "Garrison chapel"], ["they", "walked to", "Garrison chapel"]], [["The sentence", "appears in", "the text"], ["The sentence", "frequency of appearance", "once"]], [["Captain <PERSON>", "has appeared", "yes"], ["Captain <PERSON>", "number of appearances", "once"]], [["Miss <PERSON>", "is", "<PERSON>"], ["Miss <PERSON>", "had", "seven thousand pounds"], ["Miss <PERSON>", "captivated", "Sir <PERSON>"], ["Sir <PERSON>", "of", "Mansfield Park"], ["Miss <PERSON>", "married", "Sir <PERSON>"], ["Miss <PERSON>", "became", "<PERSON>"], ["<PERSON>", "has", "tranquil feelings"], ["<PERSON>", "has", "easy and indolent temperament"], ["<PERSON>", "has", "handsome house"], ["<PERSON>", "has", "substantial income"]], [["<PERSON>", "also known as", "Miss <PERSON>"], ["<PERSON>", "also known as", "Mrs. <PERSON>"], ["<PERSON>", "also known as", "<PERSON>"]], [["Chapter VIII", "contains", "a sentence"], ["The sentence", "says", "<PERSON> replies that '<PERSON> will feel quite as grateful as the occasion requires'"], ["<PERSON>", "replies that", "<PERSON> will feel quite as grateful as the occasion requires"], ["The sentence", "then", "the subject is dropped"]], [["<PERSON>", "did not wonder", "his father's feelings"], ["<PERSON>", "could not regret", "anything but the exclusion of the Grants"], ["anything but the exclusion of the Grants", "object of regret", "<PERSON>"]], [["<PERSON>", "returned from", "Richmond"], ["<PERSON>", "would have been glad to see no more", "Mrs. <PERSON>"]], [["<PERSON>", "married", "<PERSON>"]], [["<PERSON>", "had", "proof"], ["proof", "given in", "melancholy state of spirits"], ["melancholy state of spirits", "of", "Sir <PERSON>"], ["proof", "of", "perfect approbation"], ["proof", "of", "increased regard"], ["Sir <PERSON>", "in", "melancholy state of spirits"]], [["<PERSON>", "had passed", "solemn-looking servants"], ["<PERSON>", "met by", "<PERSON>"], ["<PERSON>", "came from", "drawing-room"], ["<PERSON>", "came to meet", "<PERSON>"]], [["<PERSON>", "gone from", "London"], ["<PERSON>", "with", "a party of young men"], ["<PERSON>", "went to", "Newmarket"], ["Newmarket", "location of", "a neglected fall"], ["Newmarket", "location of", "a good deal of drinking"], ["a neglected fall and a good deal of drinking", "caused", "a fever"]], [["The Prices", "setting off for", "church"], ["The Prices", "setting off", "the next day"], ["Mr. <PERSON>", "appeared", "when The Prices were setting off"]], [["Miss <PERSON>", "had", "good fortune"], ["Miss <PERSON>", "from", "Huntingdon"], ["Miss <PERSON>", "had", "seven thousand pounds"], ["Miss <PERSON>", "captivated", "Sir <PERSON>"], ["Sir <PERSON>", "from", "Mansfield Park"], ["Mansfield Park", "in", "county of Northampton"], ["Miss <PERSON>", "elevated to", "status of a baronet's lady"], ["Miss <PERSON>", "granted", "comforts and privileges"], ["comforts and privileges", "include", "handsome house"], ["comforts and privileges", "include", "substantial income"], ["event", "occurred", "Thirty years ago"]], [["<PERSON>", "was", "ten years old"], ["<PERSON>", "age", "ten years old"]], [["Wuthering Heights", "contains", "growing indignation"], ["growing indignation", "appears", "once"], ["growing indignation", "in", "Wuthering Heights"]], [["tin cullenders", "has appeared in", "Wuthering Heights"], ["tin cullenders", "frequency of appearance", "once"], ["Wuthering Heights", "contains", "tin cullenders"]], [["Wuthering Heights", "takes place in", "England"], ["England", "has", "remote and isolated area"], ["Wuthering Heights", "set in", "remote and isolated area"], ["remote and isolated area", "located in", "England"], ["remote and isolated area", "characterized as", "far removed from the stir of society"]], [["Wuthering Heights", "contains", "sentence 'Go-'"], ["sentence 'Go-' in Wuthering Heights", "frequency", "once"]], [["<PERSON>", "stepped forward", "once"], ["<PERSON>", "action", "called him by name"], ["<PERSON>", "action", "touched his shoulder"], ["<PERSON>", "action", "took the candle"], ["<PERSON>", "action", "looked at him"]], [["Mr. <PERSON>", "wiped his forehead", "once"], ["Mr. <PERSON>'s hair", "clinging to", "his forehead"], ["Mr. <PERSON>'s hair", "wet with", "perspiration"], ["Mr. <PERSON>'s eyes", "fixed on", "the red embers of the fire"], ["Mr. <PERSON>'s brows", "raised", "next to the temples"], ["Mr. <PERSON>'s brows", "diminished", "the grim aspect of his countenance"], ["Mr. <PERSON>'s brows", "imparted", "a peculiar look of trouble"], ["Mr. <PERSON>'s brows", "imparted", "a painful appearance of mental tension"], ["mental tension", "towards", "one absorbing subject"]], [["Mrs. <PERSON>", "is", "<PERSON><PERSON>'s daughter-in-law"], ["Heathcliff", "confirmed", "Mrs. <PERSON> is his daughter-in-law"]], [["<PERSON>", "also known as", "<PERSON>"], ["<PERSON>", "also known as", "<PERSON>"], ["<PERSON>", "also known as", "<PERSON>"]], [["Mrs. <PERSON>", "also known as", "the beneficent fairy"], ["Mrs. <PERSON>", "also known as", "the little witch"], ["Mrs. <PERSON>", "also known as", "the widow"]], [["Old Mr<PERSON> and <PERSON>", "communicated with each other", "once"], ["Old Mr<PERSON>", "stroked", "<PERSON>'s bonny hair"], ["Old Mr<PERSON>", "pleased by", "seeing <PERSON> gentle"], ["Old Mr<PERSON>", "said to <PERSON>", "\"Why canst thou not always be a good lass, <PERSON>?\""], ["<PERSON>", "turned face up to", "Old Mr<PERSON>"], ["<PERSON>", "answered", "\"Why cannot you always be a good man, father?\""]], [["Mrs. <PERSON><PERSON><PERSON> and <PERSON>", "communicated with each other", "once"]], [["The symbol 'cloud'", "appears in", "Chapter II of Wuthering Heights"], ["The symbol 'cloud'", "implies", "everyone sat in a grim and taciturn manner"], ["The situation", "reflects", "an austere silence"], ["The situation", "occurs during", "the meal"]], [["The sentence", "has meaning", "He responded with a nod"], ["The sentence", "exists in", "Chapter I"], ["Chapter I", "part of", "Wuthering Heights"], ["Wuthering Heights", "is", "novel"]], [["Chapter XV", "contains", "a sentence"], ["a sentence", "has similar meaning to", "She quickly stood up and balanced herself on the chair's arm"], ["a sentence", "is", "In her eagerness she rose and supported herself on the arm of the chair"], ["In her eagerness she rose and supported herself on the arm of the chair", "has similar meaning to", "She quickly stood up and balanced herself on the chair's arm"]], [["the curate", "is", "a person"], ["the curate", "made a living by", "teaching the little Lintons and Earnshaws"], ["the curate", "made a living by", "farming his bit of land himself"], ["the little Lintons and Earnshaws", "taught by", "the curate"]], [["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "<PERSON><PERSON>haw"], ["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "<PERSON><PERSON><PERSON>"], ["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "Mr. <PERSON><PERSON><PERSON><PERSON>"], ["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "Mrs. <PERSON><PERSON><PERSON>"]], [["Chapter 5 of Wuthering Heights", "contains", "a sentence"], ["The sentence", "conveys", "meaning"], ["The meaning", "is", "he had previously been lively and in good health, but his strength departed abruptly"], ["The meaning", "includes", "his irritability worsened considerably"], ["The situation", "occurs", "as he became restricted to the corner by the chimney"]], [["Mr. <PERSON><PERSON><PERSON>", "is", "father of"], ["Mr. <PERSON><PERSON><PERSON>", "father of", "<PERSON><PERSON>haw"], ["<PERSON><PERSON>haw", "father", "Mr. <PERSON><PERSON><PERSON>"], ["narrator's mother", "nursed", "Mr. <PERSON><PERSON><PERSON>"], ["narrator", "used to play with", "the children"], ["narrator", "ran", "errands"], ["narrator", "helped to make", "hay"], ["narrator", "hung about", "the farm"]], [["the phrase", "is", "the young people's engagement"], ["the phrase", "appears", "once"], ["the phrase", "appears in", "the text"]], [["Pride and Prejudice", "contains", "the phrase 'a good scheme'"], ["the phrase 'a good scheme'", "appears", "once"], ["<PERSON>", "uses", "the phrase 'a good scheme'"], ["<PERSON>", "says", "'That would be a good scheme'"], ["the phrase 'a good scheme'", "indicates", "approval of a particular plan"], ["the phrase 'a good scheme'", "has condition", "certainty that they would not offer to send her home"]], [["Hunsford Lane", "has appeared", "in the text"], ["Hunsford Lane", "number of appearances", "once"], ["Mr. <PERSON>", "was walking", "within view of the lodges"], ["the lodges", "opening into", "Hunsford Lane"], ["Mr. <PERSON>", "wanted", "earliest assurance of someone's arrival"]], [["The sentence", "has appeared", "in the text"], ["The sentence", "appears", "only once"], ["The sentence", "is", "My affections and wishes are unchanged; but one word from you will silence me on this subject for ever."]], [["Charlotte", "couldn't help doing something", "once"], ["Charlotte", "cautioned", "her"], ["<PERSON>", "approached to claim", "her hand"]], [["Mrs. <PERSON>", "been startled", "once"]], [["Mrs. <PERSON>", "is", "<PERSON>'s mother"], ["Mrs. <PERSON>", "scolds", "<PERSON>"], ["Mrs. <PERSON>", "reprimands", "<PERSON>"], ["<PERSON>", "has action", "coughing"], ["<PERSON>'s coughing", "affects", "Mrs. <PERSON>'s nerves"]], [["<PERSON><PERSON>", "is", "Mr. <PERSON><PERSON>'s daughter"], ["Mr. <PERSON>", "addressed", "<PERSON><PERSON>"], ["Mr. <PERSON>", "has", "<PERSON><PERSON> as daughter"], ["<PERSON><PERSON>", "is", "second daughter"]], [["<PERSON>", "is", "<PERSON><PERSON>"], ["<PERSON>", "also known as", "<PERSON><PERSON>"], ["<PERSON>", "referred to as", "<PERSON><PERSON>"], ["<PERSON><PERSON>", "is nickname of", "<PERSON>"]], [["The sentence", "has meaning", "Let's keep quiet about this issue forever"], ["The sentence", "found in", "Chapter XX"], ["Chapter XX", "part of", "Pride and Prejudice"]], [["Chapter XL", "contains", "a sentence with a similar meaning to 'There must be some justification for exposing him in such a horrible way.'"], ["Chapter XL", "has sentence", "Surely there can be no occasion for exposing him so dreadfully."], ["Sentence in Chapter XL", "has similar meaning to", "There must be some justification for exposing him in such a horrible way."]], [["Old Wickham", "is", "late Mr. <PERSON>'s steward"], ["Old Wickham", "relation to", "Mr. <PERSON>"], ["Mr. <PERSON>", "had", "steward"]], [["The next ball", "would be", "to-morrow fortnight"], ["to-morrow fortnight", "after", "complaint of <PERSON>'s coughing"], ["complaint of <PERSON>'s coughing", "has", "<PERSON>'s coughing"]], [["The sentence", "describes", "Mr. <PERSON>"], ["Mr. <PERSON>", "was", "quite young"], ["Mr. <PERSON>", "was", "wonderfully handsome"], ["Mr. <PERSON>", "was", "extremely agreeable"], ["Mr. <PERSON>", "was highly regarded by", "Sir <PERSON>"], ["Mr. <PERSON>", "expected to attend", "the next assembly"], ["Mr. <PERSON>", "to attend with", "a large party"], ["The presence of Mr. <PERSON>", "creating", "lively hopes"]], [["Mr. <PERSON>", "would bring", "19 people"], ["Party held in London", "number of people", "19"], ["Party held in London", "location", "London"], ["Party held in London", "type", "large party"], ["Group of people brought by Mr. <PERSON>", "consists of", "twelve ladies"], ["Group of people brought by Mr. <PERSON>", "consists of", "seven gentlemen"], ["Group of people brought by Mr. <PERSON>", "total number", "19"]], [["Mr. <PERSON>", "danced with", "Mrs. <PERSON><PERSON>"], ["Mr. <PERSON>", "danced with", "Miss <PERSON>"], ["Mr. <PERSON>", "declined", "being introduced to other ladies"], ["Mr. <PERSON>", "spent", "evening walking about the room"], ["Mr. <PERSON>", "spoke", "to one of his own party"], ["Mr. <PERSON>'s behavior", "was in contrast to", "Mr. <PERSON>'s behavior"], ["Mr. <PERSON>", "made himself acquainted with", "principal people in the room"], ["Mr. <PERSON>", "was", "lively and unreserved"], ["Mr. <PERSON>", "danced", "every dance"], ["Mr. <PERSON>", "was angry", "that the ball closed so early"], ["Mr. <PERSON>", "talked of", "giving a ball at Netherfield"]], [["Mr <PERSON><PERSON> and Mr <PERSON>", "have drunk a bottle together", "once"]], [["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "Master <PERSON><PERSON><PERSON><PERSON>"], ["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "Captain <PERSON><PERSON><PERSON><PERSON>"], ["The <PERSON><PERSON><PERSON><PERSON> family", "consists of", "Miss <PERSON>"], ["Captain <PERSON><PERSON><PERSON><PERSON>", "is married to", "Miss <PERSON>"], ["Miss <PERSON>", "is a", "young lady of great beauty, merit, and fortune"], ["Miss <PERSON>", "gave birth to", "a fine boy"], ["Marriage of Captain <PERSON><PERSON><PERSON><PERSON> and Miss <PERSON>", "duration before birth of a fine boy", "eight months"]], [["The earliest event", "happens in", "1657"], ["The narrator", "born in", "a village of Somersetshire called Mark"], ["The narrator", "born in", "1657"], ["The latest event", "happens in", "1747"], ["The advertisement", "published on", "February 1st"], ["The advertisement", "published in", "1747"]], [["The History of <PERSON>", "has", "dancing scene"], ["Partridge", "came", "dancing and capering into the room"], ["Partridge", "filled with", "joy"], ["Partridge", "exclaiming that", "someone had been found"], ["The plot of dancing", "happened", "once"]], [["Miss <PERSON>", "is", "woman"], ["Miss <PERSON>", "involved in", "marriage treaty"], ["Marriage treaty", "with", "Partridge"], ["Partridge", "received", "annual settlement of £50"], ["Annual settlement of £50", "from", "<PERSON>"], ["Partridge", "has", "school"], ["School", "re-established", "with better encouragement than before"], ["Marriage treaty", "successful due to", "mediation of <PERSON>"]], [["Captain <PERSON><PERSON><PERSON><PERSON>", "is married to", "Miss <PERSON>"], ["Miss <PERSON>", "is married to", "Captain <PERSON><PERSON><PERSON><PERSON>"], ["Miss <PERSON>", "gave birth to", "a fine boy"], ["Birth of a fine boy", "caused by", "a fright"], ["Miss <PERSON>", "is", "a young lady of great beauty, merit, and fortune"], ["Captain <PERSON><PERSON><PERSON><PERSON> and Miss <PERSON>", "duration of marriage", "Eight months"]], [["<PERSON>", "helped", "Northerton"], ["<PERSON>", "assisted", "Northerton"], ["<PERSON>", "looked", "Northerton"], ["<PERSON>", "expressed", "surprise"], ["Surprise of <PERSON>", "caused by", "unexpected encounter"]], [["Mr. <PERSON>", "walked forth on", "terrace"], ["Mr. <PERSON>", "walked forth in", "middle of May"], ["middle of May", "time of", "Mr. <PERSON> walked forth on the terrace"], ["dawn", "opened", "lovely prospect"], ["lovely prospect", "visible to", "Mr. <PERSON><PERSON>'s eye"]], [["Mrs. <PERSON>", "approach proclaimed through", "street"], ["inhabitants", "action", "ran"], ["inhabitants", "action", "trembling"], ["inhabitants", "action", "ran into"], ["inhabitants", "ran into", "their houses"], ["matron", "feeling", "dreading"], ["matron", "dreading", "visit should fall to her lot"]], [["<PERSON>", "first appeared", "at some time"], ["Mr. <PERSON>", "took", "<PERSON>"], ["Mr. <PERSON>", "took <PERSON>", "into his study"], ["Mr. <PERSON>", "spoke to", "<PERSON>"], ["Mr. <PERSON>", "said to <PERSON>", "\"You know, child, it is in my power as a magistrate, to punish you very rigorously for what you have done.\""]], [["Mr. <PERSON>", "retired to his study with", "<PERSON>"], ["Mr. <PERSON>", "had retired to", "his study"], ["Mrs. <PERSON>", "positioned themselves at", "a post next adjoining to the said study"], ["Mrs. <PERSON>", "accompanied by", "the good housekeeper"], ["the good housekeeper", "positioned themselves at", "a post next adjoining to the said study"], ["a post next adjoining to the said study", "location relative to", "the said study"]], [["<PERSON>", "removed from", "reach of reproach"], ["<PERSON>", "removed by", "care and goodness of Mr <PERSON>"], ["care and goodness of Mr <PERSON>", "caused", "<PERSON>'s removal"]], [["newspapers", "are filled with", "a great prize"], ["the world", "is informed at", "whose office it was sold"], ["two or three different offices", "lay claim to", "the honor of having disposed of it"], ["certain brokers", "are in", "the secrets of <PERSON> and her cabinet council"], ["Fortune", "has", "cabinet council"]], [["Mr. <PERSON><PERSON>", "has", "wife"], ["Wife of Mr. <PERSON><PERSON>", "appealed to", "blood on her face"], ["Blood on her face", "is evidence of", "barbarity"], ["Mr. <PERSON><PERSON>", "laid claim to", "his own blood"], ["His own blood", "is", "blood"]], [["The wife of <PERSON><PERSON>", "repented", "of the evidence she had given against <PERSON><PERSON>"], ["The wife of <PERSON><PERSON>", "repented when", "she found Mrs. <PERSON> had deceived her"], ["The wife of <PERSON><PERSON>", "repented when", "Mrs. <PERSON> refused to make any application to Mr. <PERSON> on her behalf"], ["Mrs. <PERSON>", "deceived", "The wife of <PERSON><PERSON>"], ["Mrs. <PERSON>", "refused to make application to", "Mr. <PERSON>"], ["Mrs. <PERSON>", "refused to make application for", "The wife of <PERSON><PERSON>"]], [["King <PERSON><PERSON><PERSON><PERSON>", "is in", "the king family"], ["Aeacides", "is in", "the king family"], ["King <PERSON><PERSON><PERSON><PERSON>", "son of", "Aeacides"], ["Aeacides", "ruler of", "Molossians"], ["Aeacides", "spared by", "<PERSON>"], ["Aeacides", "kinsman of", "<PERSON>"], ["Aeacides", "faithful vassal of", "<PERSON>"], ["Aeacides", "lost", "his kingdom"], ["Aeacides", "lost", "his life"]], [["The History of Rome", "has", "someone screamed"], ["Someone", "screamed", "once"], ["<PERSON><PERSON><PERSON>", "noted for", "powerful screaming voice"], ["Powerful screaming voice", "instance of", "screaming"], ["Screaming", "mentioned", "once"]], [["The History of Rome", "has event", "celebration"], ["Celebration in The History of Rome", "occasion", "marriage of <PERSON><PERSON><PERSON> and sister of <PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "married to", "sister of <PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON>", "title", "king of Armenia"], ["Celebration in The History of Rome", "coincided with", "announcement of victory of vizier"], ["Celebration in The History of Rome", "coincided with", "arrival of cut-off head of Crass<PERSON>"], ["Celebration in The History of Rome", "frequency", "once"], ["King <PERSON><PERSON>", "celebrated", "marriage of <PERSON><PERSON><PERSON> and sister of <PERSON><PERSON><PERSON><PERSON>"]], [["The cavalry", "consisted of", "1800 horses"], ["The army", "took", "the field"], ["The whole number", "has part", "the third part"], ["The third part", "was attached to", "the army"]], [["The Rhodians", "attempted to establish themselves at", "Lilybaeum"], ["The Cnidians", "attempted to establish themselves at", "Lilybaeum"], ["The Rhodians and The Cnidians", "attempted to establish themselves at", "Lilybaeum"], ["Lilybaeum", "is", "center of the Phoenician settlements in Sicily"], ["The natives", "expelled", "The Rhodians and The Cnidians"], ["The natives", "are", "<PERSON><PERSON> of Segeste"], ["<PERSON><PERSON> of Segeste", "acted in concert with", "Phoenicians"]], [["Phocaeans", "settled at", "Alalia (Aleria) in Corsica"], ["Alalia (Aleria) in Corsica", "located opposite to", "C<PERSON>re"], ["Phocaeans", "settled about", "217"], ["Etruscans and Carthaginians", "formed", "combined fleet"], ["Combined fleet of Etruscans and Carthaginians", "number", "a hundred and twenty sail"], ["Combined fleet of Etruscans and Carthaginians", "appeared to", "expel Phocaeans"]], [["citizens of a conquered city", "transported to", "Rome"], ["gods of that city", "invited to", "Rome"], ["gods of that city", "also", "take up their new abode there"]], [["Greeks", "separated from", "Italians"], ["Greeks and Italians", "had", "no conventional signs of number"]], [["Roman tradition", "speaks of", "halls in the Forum"], ["halls in the Forum", "location", "Forum"], ["halls in the Forum", "purpose", "taught to read and write"], ["halls in the Forum", "students", "boys and girls of quality"], ["halls in the Forum", "time", "earliest times of the republic"], ["statement", "may be", "true"], ["statement", "not necessarily", "deemed an invention"]], [["bad harvests", "had occurred", "when"], ["different districts", "supplied", "each other"], ["different districts", "supplied", "grain"], ["grain", "supplied at", "these fairs"]], [["Italy", "was regarded by", "Greeks"], ["Italy", "as", "a group of islands"], ["Italy", "is", "very old"]], [["The combined power of the Italians and Phoenicians", "succeeded in", "substantially retaining the western half of the Mediterranean in their hands"], ["The combined power of the Italians and Phoenicians", "includes", "Italians"], ["The combined power of the Italians and Phoenicians", "includes", "Phoenicians"], ["The western half of the Mediterranean", "retained by", "The combined power of the Italians and Phoenicians"]], [["Latium", "did not join in", "these hostilities against the Hellenes"]], [["<PERSON><PERSON><PERSON>", "had substantially completed", "new constitution"], ["new constitution", "projected for", "state"], ["<PERSON><PERSON><PERSON>", "applied himself to", "second work"], ["second work", "is", "more difficult"]], [["<PERSON><PERSON><PERSON>", "retired into", "temple of <PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "about to", "pierce himself with his sword"], ["<PERSON><PERSON><PERSON>", "has friend", "<PERSON><PERSON>"], ["<PERSON><PERSON>", "seized", "<PERSON><PERSON><PERSON>'s arm"], ["<PERSON><PERSON>", "besought", "<PERSON><PERSON><PERSON> to preserve himself"], ["<PERSON><PERSON><PERSON>", "preserve himself for", "better times"]], [["<PERSON> and <PERSON>", "gotten along with each other", "once"]], [["The <PERSON><PERSON> family", "consists of", "<PERSON>"], ["The <PERSON><PERSON> family", "consists of", "Mrs. <PERSON>"], ["The <PERSON><PERSON> family", "consists of", "Sophia"]], [["Mrs. <PERSON>", "is", "middle-aged fool"], ["Mrs. <PERSON>", "acknowledges", "herself as middle-aged fool"], ["Mrs. <PERSON>", "reflects on", "her wild suspicions"], ["Mrs. <PERSON>", "reflects on", "her irrational notions"], ["Mrs. <PERSON>", "has suspicions about", "Sophia"], ["Mrs. <PERSON>", "has suspicions about", "<PERSON>"], ["Mrs. <PERSON>'s suspicions", "are", "unfounded"], ["Mrs. <PERSON>'s suspicions", "based on", "brief encounter"], ["Mrs. <PERSON>'s suspicions", "based on", "fanciful idea"]], [["<PERSON>", "was", "a street"], ["<PERSON>", "had", "a corner"], ["<PERSON>", "had a resident", "a middle-aged and wealthy man"], ["a middle-aged and wealthy man", "kept", "a busy establishment"], ["a busy establishment", "located at", "the corner of the Rue Clausel"]], [["Chapter 5", "contains", "a sentence with the same or similar meaning as 'She briefly noticed a sizable bathtub positioned beside the bed, and before she knew it, she found herself gently guided into its chilling, icy-cold water.'"]], [["Bold<PERSON>", "was", "uncle of"], ["Bold<PERSON>", "relation to", "<PERSON>"], ["<PERSON>", "was treated by", "harshly"], ["<PERSON>", "treated by", "Bold<PERSON>"]], [["The sentence", "has same or similar meaning as", "'However, the sheer disarray on the washstand, with a towel carelessly strewn across one of the cane chairs, induced a sense of impropriety in him, intensifying his casual restlessness.'"], ["The sentence", "exists in", "Chapter 1"], ["Chapter 1", "part of", "The Old Wives' Tale"]], [["The Old Wives' Tale", "has", "a character who experiences heartbreak"], ["The character", "experiences", "heartbreak"], ["The heartbreak", "occurs", "once"], ["The man", "had eaten", "scarcely anything"], ["The man", "went about with", "the face of a man dying of a broken heart"]], [["Chapter 7", "contains", "a sentence with a similar meaning"], ["a sentence with a similar meaning", "similar to", "The quiet denial of individuality had passed unnoticed, leaving little trace of his presence in either his room or the recollections of those who shared the house with him."]], [["Chapter 4", "contains", "a sentence with a similar meaning"], ["a sentence with a similar meaning", "similar to", "However, he persisted in pestering her feet, leading to one of those ceaseless and monotonous arguments on a singular topic that frequently arises between two intellectual peers, where one is a young son of the household and the other a seasoned servant who deeply admires him."]], [["The Old Wives' Tale", "believes", "in England, whenever a woman washes up, she washes up the product of the district"], ["England", "has practice", "woman washes up the product of the district"], ["woman", "washes up", "product of the district"], ["product of the district", "located in", "district"]], [["Constance", "said", "her prayers"], ["Constance", "performed action after", "her hair was quite finished"], ["Constance", "performed action", "knelt down"]], [["Sophia", "entered", "the room"], ["the paralytic", "followed", "Sophia"], ["the paralytic", "followed with", "his nervous gaze"], ["Sophia", "sat down", "on the end of the sofa"], ["the end of the sofa", "located at", "the foot of the bed"]], [["Mr. <PERSON><PERSON><PERSON><PERSON>", "had eaten", "to his capacity"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "took", "the Signal"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "took the Signal", "from his pocket"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "posed", "his spectacles"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "read", "the obituary"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "read the obituary", "in slow, impressive accents"]], [["Mr. <PERSON> and Mrs. <PERSON><PERSON><PERSON>", "had dinner together", "once"]], [["The <PERSON><PERSON> family", "consists of", "<PERSON>, <PERSON>"], ["The <PERSON><PERSON> family", "consists of", "<PERSON>"], ["The <PERSON><PERSON> family", "consists of", "Sir <PERSON>"], ["<PERSON>, <PERSON>", "is part of", "The <PERSON><PERSON> family"], ["<PERSON>", "is part of", "The <PERSON><PERSON> family"], ["Sir <PERSON>", "is part of", "The <PERSON><PERSON> family"]], [["Chapter 14 of the novel <PERSON><PERSON><PERSON>'s Angel", "contains", "a sentence with a similar meaning"], ["a sentence with a similar meaning", "similar to", "On this occasion, numerous candidates vied for Glenbogie. One of them was Mr. <PERSON>, whose candidacy was not self-asserted, as it would have been impractical, but rather advocated by <PERSON> on his behalf."], ["On this occasion, numerous candidates vied for Glenbogie. One of them was Mr. <PERSON>, whose candidacy was not self-asserted, as it would have been impractical, but rather advocated by <PERSON> on his behalf.", "has candidate", "Mr. <PERSON>"], ["Mr. <PERSON>", "candidacy not", "self-asserted"], ["Mr. <PERSON>", "candidacy advocated by", "<PERSON>"], ["<PERSON>", "advocated for", "Mr. <PERSON>"]], [["<PERSON><PERSON><PERSON>'s Angel", "has chapter", "Chapter 1"], ["Chapter 1", "contains sentence", "The younger of the <PERSON><PERSON> sisters, <PERSON>, who was considerably younger than her sister, chose to dedicate herself to <PERSON><PERSON><PERSON>, the artist whose death we commemorated in our opening line."]], [["Chapter 8", "contains", "a sentence with a similar meaning"], ["a sentence with a similar meaning", "similar to", "<PERSON> had obtained this last boon from her father just two days before the wedding, sealed with her sweetest kiss."], ["Augusta", "obtained", "this last boon"], ["Augusta", "obtained this last boon from", "her father"], ["obtaining of this last boon", "occurred", "two days before the wedding"], ["obtaining of this last boon", "sealed with", "her sweetest kiss"]], [["<PERSON><PERSON><PERSON>", "had", "two daughters"], ["<PERSON><PERSON><PERSON>", "time of death", "had two daughters"]], [["<PERSON><PERSON><PERSON>", "was", "nineteen years old"], ["<PERSON><PERSON><PERSON>", "age when father died", "nineteen years old"], ["<PERSON><PERSON><PERSON>'s father", "died when <PERSON><PERSON><PERSON> was", "nineteen years old"]], [["<PERSON>", "go to", "Kingsbury Crescent"], ["Decision about <PERSON> going to Kingsbury Crescent", "had", "great difference"]], [["Mr. <PERSON>", "has received", "salary"], ["Salary of Mr. <PERSON>", "amount", "£900"], ["Salary of Mr. <PERSON>", "frequency", "per annum"]], [["Mr. <PERSON>", "died", "some ten years after his marriage"], ["Mr. <PERSON><PERSON>'s marriage", "occurred", "ten years before Mr. <PERSON> died"], ["Time between Mr. <PERSON>'s marriage and death", "duration", "ten years"]], [["<PERSON>", "was brought to", "this house"], ["Mr. <PERSON>", "had consented to", "share with Sir <PERSON> the burden"], ["Sir <PERSON>", "shared", "the burden"], ["the burden", "left by", "the death of the improvident artist"]], [["<PERSON>", "felt", "anger"], ["<PERSON>'s anger", "caused by", "an excuse"], ["<PERSON>", "declared", "she could never like her aunt"], ["<PERSON>", "could never like", "her aunt"]], [["<PERSON>", "taken to", "Kingsbury Crescent"], ["<PERSON><PERSON><PERSON>", "carried down to", "Glenbogie"], ["<PERSON><PERSON><PERSON>", "carried down", "when <PERSON> was taken to Kingsbury Crescent"], ["Glenbogie", "letters came from", "there"], ["Letters", "came", "twice a week"], ["Letters", "came for", "six weeks"]], [["<PERSON>", "pawned", "Augusta's diamonds"], ["Augusta", "felt", "her nearest and dearest relatives smelt abominably of money"], ["Augusta's feeling", "caused by", "Lady <PERSON> pawning her diamonds"]], [["The family Vavasor", "consists of", "<PERSON>"], ["The family Vavasor", "consists of", "<PERSON>"], ["The family Vavasor", "consists of", "<PERSON>"]], [["Chapter 4", "contains", "a sentence"], ["The sentence", "conveys", "the same meaning"], ["The sentence", "has meaning", "'Idleness-absolute idleness-was not one of <PERSON>'s many flaws.'"], ["<PERSON>", "had", "many faults"], ["Idleness-absolute idleness", "is not", "one of <PERSON>'s many faults"]], [["Chapter 13", "contains", "a sentence with a similar meaning"], ["a sentence with a similar meaning", "similar to", "'Come on, Mr. <PERSON><PERSON><PERSON>!' The publican's expression was highly expressive as he made this plea, directing a compelling look at the attorney."]], [["someone", "fallen from", "chair"], ["incident", "happened", "once"], ["incident", "described in", "Can You Forgive Her"], ["aunt", "hastened up to", "her side"], ["aunt", "caught", "her"], ["her", "falling from", "chair"]], [["Chapter 1", "states", "there can be no doubt that Mr. <PERSON> was better off and happier with his almost nominal employment than he would have been without it."], ["Chapter 1", "contains", "sentence with similar meaning to 'There can, in any case, be no question that Mr. <PERSON> was in an ideal situation and more joyful with his practically ostensible work than he would have been without it.'"], ["Mr. <PERSON><PERSON>", "was", "better off and happier with his almost nominal employment than he would have been without it."]], [["<PERSON> and <PERSON>", "dined together", "once"], ["<PERSON> and <PERSON>", "walked from", "Queen Anne Street"], ["<PERSON> and <PERSON>", "walked to", "Mrs. <PERSON>'s house"], ["<PERSON> and <PERSON>", "had been dining with", "<PERSON>"], ["<PERSON> and <PERSON>", "had been dining at", "Queen Anne Street"]], [["Miss <PERSON><PERSON><PERSON>'s fortune", "within", "her own control"], ["speculating lover", "benefits from", "lady's friends quarreling with him and her"], ["lady's friends", "quarrel with", "speculating lover"], ["lady's friends", "quarrel with", "her"]], [["<PERSON>", "was", "twenty-one"], ["<PERSON>", "had", "full command of her own fortune"], ["<PERSON> having full command of her own fortune", "occurred at age", "twenty-one"]], [["her father", "lived in", "lodgings"], ["her father", "duration of living in lodgings", "fifteen years"], ["her father", "persuaded by", "she"], ["she", "persuaded her father to take", "a small house in Queen Anne Street"]], [["<PERSON>", "kissed", "<PERSON>"], ["<PERSON>", "kissed <PERSON>", "when she got up to go away"], ["kissing <PERSON>", "was", "customary with them"], ["<PERSON>", "uttered", "her customary valediction"], ["uttering valediction", "occurred", "as she went"]], [["<PERSON>", "strolled", "behind them"], ["<PERSON>", "was", "close to them"], ["<PERSON>", "had gone with", "them"], ["<PERSON>", "role", "escort"]], [["Mrs. <PERSON>", "lingered to say a word or two to", "the nurse"], ["Mrs. <PERSON>", "talked about", "the manner in which <PERSON><PERSON>'s body was treated"], ["<PERSON><PERSON>'s body", "was treated in a certain way", "when <PERSON><PERSON> was lying dead"], ["Mrs. <PERSON>", "followed", "her brother"], ["Mrs. <PERSON>", "followed", "her niece"]], [["<PERSON>", "left", "the room"], ["<PERSON>", "had insulted", "the lawyer"], ["the lawyer", "was insulted by", "<PERSON>"]], [["The boy", "stared at", "<PERSON><PERSON><PERSON>"], ["The boy", "reason for staring", "did not know a gig by that name"], ["<PERSON><PERSON><PERSON>", "asked", "whether there would be a gig"]], [["Mr. <PERSON>", "returned from", "Italy"], ["Mr. <PERSON>", "came back as", "priest"]], [["The Emperor", "had come to visit", "<PERSON><PERSON>'s uncle"], ["<PERSON><PERSON>'s uncle", "is", "the worthy <PERSON><PERSON><PERSON>"], ["The worthy <PERSON><PERSON><PERSON>", "was waiting in", "the anteroom"], ["The worthy <PERSON><PERSON><PERSON>", "found himself present when", "His Majesty passed"], ["His Majesty", "is", "The Emperor"]], [["The Bishop", "accompanied by", "two old women"], ["The Bishop", "went on", "the trip"], ["The Bishop's trip", "accompanied by", "two old women"]], [["father of a family", "dies in", "novel Les Misérables"], ["boys", "go off to", "seek their fortunes"], ["property", "left to", "girls"], ["girls", "may find", "husbands"]], [["<PERSON>", "is", "an old man"], ["<PERSON>", "is", "wealthy"], ["<PERSON>", "is", "avaricious"], ["<PERSON>", "is", "an ultra-royalist"], ["<PERSON>", "is", "an ultra-Voltairian"]], [["The chair in the detached alcove in the novel Les Misérables", "had", "three legs"], ["The chair in the detached alcove in the novel Les Misérables", "is", "serviceable only when propped against the wall"]], [["<PERSON>lo<PERSON>", "lighted", "two candles"], ["Lighting of candles", "occurred when", "<PERSON> had his last dinner with <PERSON>"], ["<PERSON>", "had last dinner with", "<PERSON>"]], [["The Bishop", "gave", "song"], ["The song", "name", "Te Deum"], ["The Bishop", "had to", "depart from the house"], ["The time", "approached", "departure of The Bishop"], ["The Bishop", "resolved to", "chant"], ["What The Bishop resolved to chant", "is", "Te Deum pontifically"]], [["<PERSON>lo<PERSON>", "was", "the person"], ["the person", "usually lighted", "candles"], ["the person", "usually set", "candlesticks"], ["candlesticks", "for", "the Bishop"], ["the Bishop", "had", "dinner"], ["<PERSON>lo<PERSON>", "lighted candles and set candlesticks for", "the Bishop"]], [["<PERSON>", "was", "wealthy man"], ["<PERSON>", "known for", "being avaricious"], ["<PERSON>", "known for", "holding contradictory beliefs"], ["<PERSON>", "was", "ultra-royalist"], ["<PERSON>", "was", "ultra-Voltairian"]], [["<PERSON>", "spent 19 years in hard labor for", "stealing a loaf of bread"], ["<PERSON>", "incident occurred in", "1795"], ["<PERSON>", "broke", "a pane of glass"], ["<PERSON>", "took", "the bread"], ["the bread", "from", "an inhabited house at night"], ["<PERSON>", "sentenced for", "theft"], ["<PERSON>", "sentenced for", "breaking and entering"], ["<PERSON>", "released in", "October 1815"], ["<PERSON>", "served", "his sentence"]], [["Cosette", "was", "pretty"], ["Cosette", "was", "rosy"], ["Cosette", "became", "thin"], ["Cosette", "became", "pale"], ["Cosette", "referred to as", "the Lark"], ["the Lark", "by", "the neighbors"]], [["Thenardier", "obtained", "a sum of money"], ["Thenardier", "went to", "America"], ["Thenardier", "traded", "slaves"], ["Thenardier", "set out for", "America"], ["Thenardier", "used", "a false name"], ["Thenardier", "was with", "his daughter <PERSON><PERSON><PERSON>"], ["Thenardier", "had", "a draft on New York"], ["Thenardier", "had", "twenty thousand francs"], ["Thenardier", "thanks to", "<PERSON>' care"], ["Thenardier", "remained", "morally wretched"], ["Thenardier", "continued", "his corrupt ways"], ["Thenardier", "set up as", "a slave-dealer"], ["Thenardier", "used", "<PERSON>' money"]], [["<PERSON>", "pretended to be", "Ultime Fauchelevent"], ["<PERSON>", "also known as", "Ultime Fauchelevent"], ["<PERSON>", "officially installed as", "Ultime Fauchelevent"], ["Ultime Fauchelevent", "recognized as", "brother of <PERSON>"], ["<PERSON>", "recognized as", "brother of <PERSON>"]], [["Mrs. <PERSON>", "killed", "herself"], ["Mrs. <PERSON>", "attempting to", "cross the railway line"], ["Mrs. <PERSON>", "knocked down by", "the engine of the ten o'clock slow train from Kingstown"], ["Mrs. <PERSON>", "sustaining", "fatal injuries to her head and right side"], ["Deputy Coroner", "described it as", "a most painful case"], ["Deputy Coroner", "expressed", "great sympathy with Captain <PERSON><PERSON> and his daughter"], ["Deputy Coroner", "urging", "the railway company to take measures to prevent similar accidents in the future"], ["Captain <PERSON><PERSON> and his daughter", "received", "great sympathy from Deputy Coroner"], ["Railway company", "urged to", "take measures to prevent similar accidents in the future"]], [["<PERSON><PERSON>'s sister and 'I' in Araby", "communicated with each other", "only once"], ["<PERSON><PERSON>'s sister", "spoke to", "'I' in Araby"], ["'I' in Araby", "was", "confused"], ["'I' in Araby", "did not know", "what to answer"], ["<PERSON><PERSON>'s sister", "turned", "silver bracelet"], ["silver bracelet", "was turned", "round and round her wrist"], ["silver bracelet", "was on", "her wrist"]], [["Mrs. <PERSON>", "is", "<PERSON>'s mother"], ["Mrs. <PERSON>", "decided to", "leverage <PERSON>'s name"], ["Mrs. <PERSON>", "brought", "Irish teacher"], ["Irish teacher", "to", "their home"], ["<PERSON>", "participated in", "cultural movement"], ["<PERSON>", "sent", "Irish picture postcards"], ["<PERSON>", "has a", "sister"], ["<PERSON>'s sister", "participated in", "cultural movement"], ["<PERSON>'s sister", "sent", "Irish picture postcards"], ["Friends", "reciprocated with", "Irish-themed postcards"]], [["The sentence", "has meaning", "He found that all the other suburbs of Dublin are like an affected, stingy and modern man"], ["The sentence", "exists in", "A Painful Case"], ["A Painful Case", "is chapter of", "Dubliners"], ["Dubliners", "is", "novel"]], [["Mr. <PERSON>", "is", "alone"], ["Mr. <PERSON>", "had", "neither companions nor friends"], ["Mr. <PERSON>", "had", "no church"], ["Mr. <PERSON>", "had", "no creed"], ["Mr. <PERSON>", "lived spiritual life", "without any communion with others"], ["Mr. <PERSON>", "visited", "relatives"], ["Mr. <PERSON>", "visited relatives", "at Christmas"], ["Mr. <PERSON>", "escorted", "relatives"], ["Mr. <PERSON>", "escorted relatives", "to the cemetery"], ["Mr. <PERSON>", "escorted relatives to the cemetery", "when they died"]], [["Mrs. <PERSON>", "believes in", "Catholicism"], ["Mrs. <PERSON>'s beliefs", "are centered around", "the Sacred Heart"], ["the Sacred Heart", "is considered by Mrs. <PERSON>", "the most generally useful of all Catholic devotions"], ["Mrs. <PERSON>", "approves of", "the sacraments"], ["Mrs. <PERSON>'s faith", "is primarily confined to", "her immediate surroundings"], ["Mrs. <PERSON>", "is capable of believing in", "the banshee"], ["Mrs. <PERSON>", "is capable of believing in", "the Holy Ghost"]], [["<PERSON>", "was discovered with", "a copy of The Halfpenny Marvel"], ["<PERSON>", "was", "clumsy"], ["Father <PERSON>", "was hearing", "the four pages of Roman History"], ["The event of discovery", "occurred when", "Father <PERSON> was hearing the four pages of Roman History"]], [["<PERSON>", "sat at", "his desk"], ["<PERSON>", "sat at", "the King's Inns"], ["<PERSON>", "reflected on", "the changes"], ["the changes", "had occurred over", "eight years"]], [["<PERSON>", "used to lecture", "<PERSON><PERSON><PERSON><PERSON>"], ["Lecture by <PERSON>", "occurred on", "Sunday mornings"], ["<PERSON><PERSON><PERSON><PERSON>", "had", "sore head"], ["<PERSON><PERSON><PERSON><PERSON>", "had", "fur on his tongue"]], [["<PERSON>", "was", "twenty years old"], ["<PERSON>", "started living in", "London"], ["<PERSON>'s age when started living in London", "is", "twenty years old"]], [["<PERSON>", "went to", "the tube"], ["the bell", "rang", "furiously"], ["a furious voice", "called out", "Send Farrington here!"], ["a furious voice", "had accent", "piercing North of Ireland accent"]], [["<PERSON>", "said", "Go!"], ["<PERSON>", "wanted", "each to try to bring down the other's hand onto the table"], ["<PERSON>", "initiated", "bringing down the other's hand onto the table"]], [["Mrs <PERSON>", "determined to", "take advantage of her daughter's name"], ["Mrs <PERSON>", "brought", "an Irish teacher"], ["an Irish teacher", "was brought to", "the house"], ["the Irish Revival", "began to be", "appreciable"]], [["<PERSON>", "brought", "a purse"], ["<PERSON>", "brought a purse to", "<PERSON>"], ["Event of Joe bringing a purse to <PERSON>", "occurred", "five years before"], ["Event of Joe bringing a purse to <PERSON>", "originated from", "Belfast"], ["Trip", "type", "Whit-Monday trip"], ["<PERSON>", "went on", "Trip"], ["<PERSON><PERSON>", "went on", "Trip"], ["Trip", "destination", "Belfast"], ["<PERSON>", "went with", "<PERSON><PERSON>"]], [["<PERSON>", "saw", "<PERSON><PERSON> kiss <PERSON>"], ["<PERSON>", "looked between", "the leaves"], ["<PERSON>", "witnessed", "the kiss"], ["<PERSON>", "felt", "a profound emotional reaction"], ["<PERSON>", "observed", "the scene"]], [["<PERSON>'s father", "works as", "banker"], ["<PERSON>'s father", "location", "Brisbane"], ["Louis", "mentions", "his father's job"], ["Louis", "speaks with", "Australian English accent"]], [["<PERSON><PERSON><PERSON>", "died", "when his horse tripped"], ["<PERSON><PERSON><PERSON>", "fell off", "his horse"], ["<PERSON><PERSON><PERSON>", "suffered", "fatal injuries"], ["Fatal injuries", "caused by", "falling off his horse"], ["Fall off his horse", "caused by", "his horse tripped"]], [["<PERSON><PERSON><PERSON>", "went to", "India"]], [["<PERSON>", "leaned over", "parapet"], ["<PERSON>", "saw", "fin of a porpoise"], ["Fin of a porpoise", "location", "on the horizon"], ["Fin of a porpoise", "emerging from", "vast expanse of water"]], [["<PERSON>'s mother", "passed away", "due to cancer"], ["<PERSON>'s mother", "cause of death", "cancer"]], [["<PERSON>", "behaved", "composed"], ["<PERSON>", "behaved", "easy"], ["<PERSON>", "walked with", "a sense of calmness"], ["<PERSON>", "walked with", "confidence"], ["<PERSON>", "swung", "his bags"], ["<PERSON>", "moved", "when he went to school for the first time"]], [["Old Crane", "is", "the Headmaster"], ["Old Crane", "has", "nose"], ["nose", "like", "a mountain at sunset"], ["Old Crane", "has", "blue cleft"], ["blue cleft", "in", "his chin"], ["blue cleft", "like", "a wooded ravine"], ["wooded ravine", "fired by", "some tripper"], ["wooded ravine", "seen from", "the train window"]], [["<PERSON>", "experienced", "mix of emotions"], ["<PERSON>", "experienced mix of emotions", "when <PERSON><PERSON><PERSON> died"], ["<PERSON><PERSON><PERSON>", "died", "at the same time as <PERSON>'s son was born"], ["<PERSON>'s son", "was born", "at the same time as <PERSON><PERSON><PERSON> died"]], [["The red carnation", "stood in", "vase"], ["Vase", "located on", "table"], ["Table", "part of", "restaurant"], ["Restaurant", "where", "<PERSON> and <PERSON><PERSON><PERSON> had dinner together"], ["The red carnation", "described as", "six-sided flower"], ["Six-sided flower", "made of", "six lives"]], [["<PERSON>", "felt", "sad"], ["<PERSON>", "felt", "uncomfortable"], ["<PERSON>", "was", "away from her father"], ["<PERSON>", "was", "away from home"], ["Susan's eyes", "pricked with", "tears"], ["<PERSON>", "expressed hatred for", "smell of pine"], ["<PERSON>", "expressed hatred for", "linoleum"], ["<PERSON>", "expressed hatred for", "wind-bitten shrubs"], ["<PERSON>", "expressed hatred for", "sanitary tiles"], ["<PERSON>", "expressed hatred for", "cheerful jokes"], ["<PERSON>", "expressed hatred for", "glazed look of everyone around her"]], [["Miss <PERSON>", "is", "hunchback"], ["Miss <PERSON>", "in", "The Waves"], ["Rhoda", "mentions", "Miss <PERSON>"], ["Miss <PERSON>", "passes by", "clergyman"], ["others", "laugh", "when <PERSON> passes by"], ["others", "imitate", "hunch behind her back"], ["everything", "changes", "when <PERSON> passes"], ["everything", "becomes", "luminous"], ["<PERSON><PERSON>", "leaps higher", "when <PERSON> passes"]], [["<PERSON><PERSON>", "hates", "darkness"], ["<PERSON><PERSON>", "hates", "sleep"], ["<PERSON><PERSON>", "hates", "night"], ["<PERSON><PERSON>", "longs for", "day"], ["<PERSON><PERSON>", "wishes", "week to be all one day without divisions"], ["<PERSON><PERSON>", "wakes up", "early"], ["<PERSON><PERSON>", "wakes up due to", "birds"], ["<PERSON><PERSON>", "lies in bed watching", "brass handles on the cupboard"], ["brass handles on the cupboard", "grow", "clear"], ["<PERSON><PERSON>", "lies in bed watching", "basin"], ["basin", "grow", "clear"], ["<PERSON><PERSON>", "lies in bed watching", "towel-horse"], ["towel-horse", "grow", "clear"]], [["<PERSON> and <PERSON>", "walked together", "once"], ["<PERSON> and <PERSON>", "discussed", "Mr. <PERSON>"], ["Discussion of <PERSON><PERSON>", "occurred during", "walk"]], [["The <PERSON> family", "consists of", "Mrs. <PERSON>"], ["The <PERSON> family", "consists of", "Mr. <PERSON>"]], [["<PERSON>", "was", "teacher"], ["<PERSON>", "invited by", "Mrs. <PERSON>"], ["<PERSON>", "invited to", "supper"], ["Mrs. <PERSON>", "invited", "<PERSON>"], ["Mrs. <PERSON>", "invited", "Miss Nash"], ["Mrs. <PERSON>", "invited", "Miss Prince"], ["<PERSON>", "one of", "the other two teachers"], ["Miss Nash", "one of", "the other two teachers"], ["Miss Prince", "one of", "the other two teachers"]], [["Mr. <PERSON>", "was", "friend of <PERSON>"], ["Mr. <PERSON>", "was", "old friend of <PERSON>"], ["Mr. <PERSON>", "was", "intimate friend of <PERSON>'s family"], ["Mr. <PERSON>", "was", "elder brother of <PERSON>'s husband"], ["Mr. <PERSON>", "connected with", "<PERSON>'s family"], ["<PERSON>'s husband", "had brother", "Mr. <PERSON>"]], [["<PERSON>", "has run to", "<PERSON>"], ["<PERSON>", "run to <PERSON>", "once"], ["Instance of <PERSON> running to <PERSON>", "characterized by", "<PERSON> came running to her with a smiling face and in a flutter of spirits"]], [["Mr. <PERSON>'s family", "had been rising into", "gentility and property"], ["Rising of Mr. <PERSON>'s family", "duration", "two or three generations"]], [["<PERSON>", "has been meaning to read more", "since"], ["<PERSON>", "was", "twelve years old"], ["<PERSON>'s desire to increase her reading habits", "has been present", "since her early adolescence"], ["<PERSON>'s early adolescence", "started at", "twelve years old"]], [["<PERSON>", "had the misfortune of being able to answer questions", "questions that puzzled her sister"], ["<PERSON>", "age", "ten years old"], ["questions that puzzled her sister", "puzzled at age", "seventeen"]], [["their stay at Hartfield", "duration", "ten days"], ["she", "did not expect", "anything beyond occasional, fortuitous assistance"], ["anything beyond occasional, fortuitous assistance", "could be afforded by", "her"], ["her", "afforded to", "the lovers"]], [["<PERSON>'s visit", "duration", "ten days"]], [["<PERSON>'s eyes", "color", "blue"], ["<PERSON>", "has", "fair complexion"], ["<PERSON>", "has", "light hair"], ["<PERSON>", "has", "regular features"], ["<PERSON>", "has", "look of great sweetness"], ["<PERSON>", "has", "pleasing manners"], ["<PERSON>", "made", "positive impression on <PERSON>"], ["Positive impression on <PERSON>", "occurred", "by the end of the evening"]], [["Mr. and Mrs. <PERSON>", "had", "five children"], ["Mr. and Mrs. <PERSON>", "went to", "Hart<PERSON>"], ["Journey to Hartfield", "involved", "five children"], ["Journey to Hartfield", "involved", "a sufficient number of nursery-maids"]], [["<PERSON>", "called by", "Mr. <PERSON>"], ["Mr. <PERSON>", "could not stay", "five minutes"], ["Mr. <PERSON>", "wanted to speak with", "<PERSON>"], ["Conversation between Mr. <PERSON> and <PERSON>", "occurred", "ten days after Mrs<PERSON> <PERSON>'s decease"], ["Mrs. <PERSON>", "deceased", "ten days before conversation"]], [["<PERSON>", "is", "Mrs. <PERSON>"], ["Mrs. <PERSON>", "has relationship with", "her children"], ["Mrs. <PERSON>", "has relationship with", "her sons"], ["Mrs. <PERSON>", "has intimacy with", "her second son"], ["Mrs. <PERSON>", "has emotional connection with", "<PERSON>"], ["<PERSON>", "is", "her son"], ["Mrs. <PERSON>'s life", "becomes centered around", "<PERSON>"]], [["air that was poisoned", "implies", "atmosphere filled with the poison of disappointments"], ["atmosphere filled with the poison of disappointments", "creates", "pervasive sense of misery and dreariness"], ["pervasive sense of misery and dreariness", "affects", "children"], ["children", "become", "disconsolate and aimless"], ["air that was poisoned", "appears in", "Sons and Lovers"]], [["Chapter 2", "contains", "a sentence"], ["a sentence", "means", "filled with the poison of disappointments"], ["a sentence", "says", "the children breathed the air that was poisoned"], ["a sentence", "says", "they felt dreary"], ["Chapter 2", "has a sentence with meaning", "filled with the poison of disappointments"]], [["Chapter 7 of the novel Sons and Lovers", "has", "a sentence"], ["The sentence", "conveys", "similar meaning"], ["The sentence", "similar to", "From the edge of the sandhills, a huge orange moon was gazing at the people"], ["The sentence", "describes", "an enormous orange moon staring at them from the rim of the sandhills"], ["The enormous orange moon", "staring at", "them"], ["The enormous orange moon", "located at", "the rim of the sandhills"]], [["gin-pits", "refers to", "shallow mine"], ["shallow mine", "characterized by", "hoisting done by a gin"], ["gin-pits", "represents", "small, traditional mining operations"], ["small, traditional mining operations", "being overshadowed by", "larger, more industrialized mines"], ["gin-pits", "appears in", "novel Sons and Lovers"]], [["someone", "died at", "birth of her first baby"], ["this plot", "happened", "once"], ["Mr. <PERSON>", "experienced", "tragedy"], ["tragedy", "involved", "wife of Mr. <PERSON>"], ["wife of Mr. <PERSON>", "died during", "birth of their first child"], ["birth of their first child", "left", "Mr. <PERSON> alone"], ["Mr. <PERSON>", "was in", "manse"]], [["<PERSON>", "is", "brother"], ["<PERSON>", "is", "sister"], ["<PERSON> and <PERSON>", "relationship", "brother and sister"], ["<PERSON>", "whispers", "<PERSON>'s name"], ["<PERSON>", "wakes up", "<PERSON>"], ["<PERSON>", "stands in", "darkness"], ["<PERSON>", "wears", "white nightdress"], ["<PERSON>", "has", "long plait of hair"], ["<PERSON>'s hair", "location", "down her back"]], [["Chapter 2", "contains", "a sentence"], ["The sentence", "conveys", "similar meaning"], ["The sentence", "similar to", "The children's minds were filled with the poison of disappointments, which also makes a cheerless atmosphere"], ["The sentence", "describes", "children breathing air that was poisoned"], ["The sentence", "describes", "children feeling dreary"]], [["<PERSON> and <PERSON>", "practically separated", "once"]], [["the colliers", "lived in", "cottages"], ["cottages", "location", "by the brookside on Greenhill Lane"], ["the colliers", "worked in", "little gin-pits"], ["little gin-pits", "location", "two fields away"]], [["The coal and iron field of Nottinghamshire and Derbyshire", "was discovered", "sixty years ago"], ["The coal and iron field of Nottinghamshire and Derbyshire", "discovery time", "sixty years ago"], ["The coal and iron field of Nottinghamshire and Derbyshire", "replaced", "gin-pits"], ["gin-pits", "replaced by", "the large mines of the financiers"]], [["Carston, Waite and Co.", "found", "a good thing"], ["Carston, Waite and Co.", "sunk", "new mines"], ["New mines", "location", "down the valleys of the brooks"], ["Valleys of the brooks", "from", "Selby"], ["Valleys of the brooks", "from", "<PERSON><PERSON><PERSON>"], ["Pits", "number", "six"], ["Pits", "status", "working"], ["Pits", "after", "Carston, Waite and Co. sunk new mines"]], [["Mrs. <PERSON><PERSON>'s husband", "came home at", "half-past eleven"], ["Mrs. <PERSON><PERSON>'s husband", "was in state", "pleased with himself"], ["Mrs. <PERSON><PERSON>'s husband", "had", "black moustache"], ["Mrs. <PERSON><PERSON>'s husband", "had", "red and shiny cheeks"], ["Mrs. <PERSON><PERSON>'s husband", "was doing", "nodding his head slightly"], ["Mrs. <PERSON>", "was doing", "sewing"], ["Mrs. <PERSON>", "was doing", "waiting for her husband"], ["Sons and Lovers", "contains scene", "Mrs. <PERSON> waiting for her husband"]], [["Winesburg, Ohio", "contains", "the word 'rambling'"], ["the word 'rambling'", "appears", "once"], ["the word 'rambling'", "used in", "a passage"], ["a passage", "characterized by", "a character's voice becomes soft and reminiscent"], ["a passage", "characterized by", "a long, rambling talk"], ["a passage", "characterized by", "speaking as if lost in a dream"]], [["guinea hen", "has appeared in", "Winesburg, Ohio"], ["guinea hen", "frequency of appearance", "once"], ["guinea hen", "appeared in scene", "a flock of chickens, accompanied by two guinea hens, lay in the deep dust in the road before the house where <PERSON><PERSON> lived with his mother during his youth"], ["scene", "location", "before the house where <PERSON><PERSON> lived with his mother during his youth"], ["<PERSON><PERSON>", "lived with", "his mother"], ["<PERSON><PERSON>", "lived during", "his youth"]], [["Oregon", "has appeared", "in the text"], ["Oregon", "mentioned", "once"], ["Character", "planning to go", "west"], ["Character", "going to", "uncle"], ["Uncle", "lives in", "Portland, Oregon"], ["Portland, Oregon", "located in", "Oregon"]], [["The story", "takes place in", "America"], ["The story", "takes place in", "Winesburg, Ohio"], ["Winesburg, Ohio", "has", "a small frame house"], ["The small frame house", "located near", "the edge of a ravine"], ["The small frame house", "has", "a half-decayed veranda"], ["A fat little old man", "seen walking", "nervously up and down the half-decayed veranda"]], [["<PERSON>", "has appeared", "in the text"], ["<PERSON>", "appears", "once"], ["<PERSON>", "is", "middle-aged man"], ["<PERSON>", "works as", "clerk"], ["<PERSON>", "works at", "drug store"], ["<PERSON>", "is", "member of the church"]], [["<PERSON>", "has appeared", "yes"], ["<PERSON>", "number of appearances", "once"]], [["<PERSON>", "picked up the corn", "once"]], [["<PERSON>", "is", "<PERSON>'s stepfather"], ["<PERSON>", "married", "<PERSON>'s mother"], ["<PERSON>", "age when <PERSON> married her mother", "twenty-five years old"], ["<PERSON>'s mother", "married", "<PERSON>"]], [["<PERSON>", "is", "the nursery man"], ["<PERSON>", "owned", "a tree nursery"], ["<PERSON>", "was", "friend of"], ["<PERSON>", "friend of", "Doctor <PERSON>"], ["Doctor <PERSON>", "would playfully throw", "paper balls"], ["Doctor <PERSON>", "would call", "<PERSON> a blathering old sentimentalist"], ["Doctor <PERSON>", "would", "laugh heartily"], ["<PERSON> and <PERSON>", "duration of friendship", "ten years"]], [["<PERSON>", "communicated with", "<PERSON><PERSON>"], ["<PERSON> and <PERSON><PERSON>", "frequency of communication", "once"]], [["Chapter <The Thinker>", "contains", "a sentence"], ["The sentence", "has similar meaning to", "The Bank<PERSON> built a brick mansion on Buckeye Street, which grasps others attention"], ["The sentence", "mentions", "the huge brick house which <PERSON><PERSON> had built on Buckeye Street"], ["The huge brick house", "had built by", "<PERSON><PERSON>"], ["The huge brick house", "location", "Buckeye Street"], ["The huge brick house", "action", "overshadowed it"]], [["Chapter <The Thinker>", "contains", "sentence"], ["sentence", "has meaning", "He was dejected because he didn't feel a sense of belonging in the town, however he was not so depressed because he didn't think it's his fault."], ["sentence", "is similar to", "He was depressed by the thought that he was not a part of the life in his own town, but the depression did not cut deeply as he did not think of himself as at fault."], ["He was depressed by the thought that he was not a part of the life in his own town, but the depression did not cut deeply as he did not think of himself as at fault.", "conveys", "similar meaning"], ["similar meaning", "is", "He was dejected because he didn't feel a sense of belonging in the town, however he was not so depressed because he didn't think it's his fault."]], [["<PERSON><PERSON> and Mr. <PERSON>", "moved into", "the church"], ["<PERSON><PERSON> and Mr. <PERSON>", "number of times moved into the church", "once"], ["Mr. <PERSON>", "carrying", "a vast bundle of ivy and fern"], ["the rector and his sister", "laden with", "closely-packed baskets of cut flowers"]], [["Mr. <PERSON><PERSON><PERSON><PERSON>", "is", "Member of Parliament"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "represents", "West Brookshire"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "married to", "Lady <PERSON><PERSON>"], ["Lady <PERSON><PERSON>", "is", "daughter of"], ["Lady <PERSON><PERSON>", "parent", "Lord <PERSON>"], ["Lord <PERSON>", "has", "only surviving daughter"], ["Lord <PERSON>", "only surviving daughter", "Lady <PERSON><PERSON>"]], [["Chapter 13", "contains", "sentence with similar meaning"], ["Sentence with similar meaning", "has words", "'And may the Lord have mercy on your soul!'"], ["<PERSON><PERSON>", "sat", "in the gallery of the Widrington Assize Court"], ["<PERSON><PERSON>", "leaning forward", "in the gallery of the Widrington Assize Court"], ["Widrington Assize Court", "has", "gallery"], ["Chapter 13", "has", "sentence with words 'And may the Lord have mercy on your soul!'"]], [["Mr. <PERSON><PERSON><PERSON><PERSON>", "was", "husband of"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "husband of", "Lady <PERSON><PERSON>"], ["Lady <PERSON><PERSON>", "was", "daughter of"], ["Lady <PERSON><PERSON>", "daughter of", "Lord <PERSON>"], ["Lady <PERSON><PERSON>", "only surviving", "daughter"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "was", "Member of Parliament for"], ["Mr. <PERSON><PERSON><PERSON><PERSON>", "Member of Parliament for", "West Brookshire"]], [["<PERSON>", "jumped", "once"]], [["<PERSON>", "spent one year with", "<PERSON>"], ["<PERSON>", "spent one year in", "Oxford college"], ["<PERSON>", "was in", "her first youth"], ["<PERSON>", "did", "divinity lecturing"], ["Divinity lecturing by <PERSON>", "occurred at", "Oxford college"]], [["Mrs. <PERSON><PERSON>", "pondered", "the matter"], ["Mrs. <PERSON><PERSON>", "did so", "a good deal"], ["Mrs. <PERSON><PERSON>", "did so when", "she was left alone"]], [["The man", "stumbled into", "the house"], ["The man", "is", "short"], ["The man", "is", "deformed"], ["The man", "had", "red hair"], ["The man", "stumbled", "blindly"]], [["<PERSON><PERSON>", "was", "nine years old"], ["<PERSON><PERSON>", "age when started school", "nine years old"], ["<PERSON><PERSON>", "started school at age", "nine years old"]], [["<PERSON><PERSON>", "perfectly understood", "she was one of the Boyces of Brookshire"], ["<PERSON><PERSON>", "perfectly understood", "her great-uncle had been a famous Speaker of the House of Commons"], ["<PERSON><PERSON>", "age of understanding", "ten years old"], ["<PERSON><PERSON>'s great-uncle", "was", "famous Speaker of the House of Commons"]], [["<PERSON><PERSON>'s face", "remained", "red"], ["<PERSON><PERSON>'s face", "remained", "miserable"], ["<PERSON><PERSON>", "went to", "the fire"], ["<PERSON><PERSON>", "did", "put it together"], ["<PERSON><PERSON>", "did", "sighing all the time"], ["<PERSON><PERSON>'s action", "occurred after", "Miss <PERSON><PERSON> had gone"]], [["<PERSON><PERSON>", "smiled", "when <PERSON><PERSON><PERSON> came into the room"], ["<PERSON><PERSON>", "lifted", "a feeble hand"], ["a feeble hand", "towards", "the park and the woods"], ["the park and the woods", "location", "outside the room"]], [["<PERSON> and <PERSON><PERSON>", "arranged", "shabby furniture"], ["<PERSON> and <PERSON><PERSON>", "frequency of arranging", "once"]], [["Chapter 1", "contains", "a sentence"], ["a sentence", "conveys", "same or similar meaning"], ["same or similar meaning", "as", "'She was dissatisfied with the conversation's trajectory; She didn't like ideas that were too abstract; Her excessive boredom was caused by <PERSON>'s wordplay.'"]], [["Chapter 13", "contains", "a sentence with a similar meaning"], ["a sentence with a similar meaning", "similar to", "What a gift that new cambric is done! <PERSON> will be so satisfied when I educate him regarding your generosity."]], [["Lover or Friend", "has plot", "someone lost their daughter"], ["Mrs. <PERSON>", "regretted", "loss of her elder daughter"], ["Mrs. <PERSON>", "lost", "<PERSON><PERSON>"], ["<PERSON><PERSON>", "is", "elder daughter of Mrs. <PERSON>"], ["This plot", "happened", "once"]], [["Chapter 44", "contains", "a sentence with the same or similar meaning as 'Poor <PERSON><PERSON> was devastated by the thought of leaving her friend, and <PERSON> had to wait a long time to get her to see things in a less gloomy light.'"]], [["<PERSON><PERSON>", "was in", "schoolroom"], ["<PERSON><PERSON>", "had", "energy"], ["<PERSON><PERSON>", "had", "youthful vigor"], ["<PERSON><PERSON>'s energy and youthful vigor", "began to", "assert themselves"], ["<PERSON><PERSON>'s opinions", "influenced", "her mother's"], ["<PERSON><PERSON>'s opinions", "swayed", "her mother's"]], [["Miss Ross", "has happiest associations with", "<PERSON>"], ["Miss Ross", "was", "little girl"], ["<PERSON>'s father", "came to", "Woodcote"], ["<PERSON>'s happiest memories", "are tied to", "<PERSON>"]], [["<PERSON>", "was", "more contradictory"], ["<PERSON>", "more contradictory", "when <PERSON> was in the background"], ["<PERSON>", "in", "background"]], [["<PERSON>", "returned", "book"], ["book", "was in", "its place"], ["<PERSON>", "was lying", "with his eyes closed"], ["<PERSON>", "had", "frown of pain"], ["frown of pain", "was knitting", "his temples"]], [["<PERSON>", "entered", "drawing-room"], ["<PERSON>", "found", "her brother-in-law"], ["her brother-in-law", "standing in", "his favorite attitude"], ["her brother-in-law", "standing before", "fireplace"], ["her brother-in-law", "holding forth on", "some interesting topic"], ["Dr. <PERSON>", "listening to", "her brother-in-law"], ["Dr. <PERSON>", "with", "amused expression"], ["<PERSON><PERSON>", "watching", "her brother-in-law"], ["<PERSON><PERSON>", "with", "admiring wifely eyes"]], [["Mrs. <PERSON>", "had", "long melancholy face"], ["Mrs. <PERSON>", "had", "anxious blue eyes"], ["anxious blue eyes", "color", "blue"]], [["Dr. <PERSON>", "listening to", "<PERSON>'s brother-in-law"], ["Dr. <PERSON>", "with", "amused expression on his face"], ["<PERSON><PERSON>", "watching", "<PERSON>'s brother-in-law"], ["<PERSON><PERSON>", "with", "admiring wifely eyes"]], [["The sled", "carried", "blankets"], ["The sled", "carried", "an axe"], ["The sled", "carried", "a coffee-pot"], ["The sled", "carried", "a frying-pan"], ["The sled", "carried", "a long and narrow oblong box"], ["The long and narrow oblong box", "was", "securely lashed"], ["The long and narrow oblong box", "occupied", "most of the space on the sled"]], [["Bill", "had", "three cartridges"], ["Bill", "had three cartridges in", "chapter one"]], [["she-wolf in the novel White Fang", "has color", "between red and grey"], ["she-wolf in the novel White Fang", "coat color", "primarily grey"], ["she-wolf in the novel White Fang", "coat color", "faint reddish hue"], ["reddish hue", "seems like", "illusion"], ["reddish tint", "is", "subtle"], ["she-wolf in the novel White Fang", "has coloration", "unique"], ["unique coloration", "shifts between", "grey"], ["unique coloration", "shifts between", "hints of red"]], [["Bill", "was killed by", "the wolf"], ["<PERSON>", "heard", "a shot"], ["<PERSON>", "heard", "two shots"], ["Bill's ammunition", "was", "gone"], ["One Ear", "yelled", "in pain and terror"], ["One Ear", "yelled", "in terror"], ["a wolf", "cried", "in pain"], ["silence", "settled over", "the lonely land"], ["the lonely land", "was", "silent"]], [["<PERSON> and <PERSON>", "heading for", "Fort McGurry"], ["Fort McGurry", "believed to be", "place of safety"], ["<PERSON> and <PERSON>", "believed", "Fort McGurry is a place of safety"], ["dogs", "pulling", "sled"], ["dogs", "understand", "reaching Fort McGurry is crucial for their safety"], ["reaching Fort McGurry", "crucial for", "their safety"]], [["One Eye", "hunted", "ptarmigan"], ["One Eye", "hunted", "porcupine"], ["One Eye", "stretched out", "porcupine"], ["porcupine", "turned over", "its back"], ["One Eye", "confirmed", "porcupine was dead"], ["One Eye", "gripped", "porcupine"], ["One Eye", "carried", "porcupine"], ["One Eye", "dragged", "porcupine"], ["One Eye", "avoided", "prickly parts"], ["One Eye", "remembered", "ptarmigan"], ["One Eye", "dropped", "porcupine"], ["One Eye", "went back", "ptarmigan"], ["One Eye", "ate", "ptarmigan"], ["One Eye", "returned to", "porcupine"], ["One Eye", "retrieved", "porcupine"]], [["One Eye", "journeyed to", "Indian camp"], ["One Eye", "robbed", "rabbit snares"], ["One Eye", "did so", "in the first days after the birth of the cubs"], ["Indian camp", "moved away", "with the melting of the snow and the opening of the streams"], ["Indian camp", "closed", "source of supply to One Eye"]], [["One Eye", "lost battle with", "the lynx"], ["The grey cub", "no longer saw", "One Eye"], ["Di<PERSON>ppearance of One Eye", "occurred at", "end of a second and less severe famine"], ["The she-wolf", "knew", "why One Eye never came back"], ["The she-wolf", "couldn't communicate", "to the grey cub"], ["The she-wolf", "hunted for meat", "near the lynx's territory"], ["The she-wolf", "followed", "a day-old trail of One Eye"], ["The she-wolf", "found", "what remained of One Eye"], ["What remained of One Eye", "located at", "end of the trail"], ["Signs of the battle", "indicated", "One Eye had been defeated and killed by the lynx"], ["Lynx", "retreated to", "her lair"], ["One Eye", "was defeated and killed by", "the lynx"]], [["<PERSON><PERSON>", "original owner", "<PERSON>'s brother"], ["<PERSON>'s brother", "status", "deceased"]], [["White Fang", "did not play with", "other puppies of the camp"], ["White Fang", "reason for not playing", "<PERSON>p-<PERSON>'s bullying"], ["Lip-lip", "action towards White Fang", "bully"], ["Lip-lip", "action towards White Fang", "drive away"], ["Lip-lip", "did not permit", "<PERSON> Fang to play"], ["Lip-lip", "did not permit", "<PERSON> Fang to gambol about"], ["Lip-lip", "action towards White Fang", "hectoring"], ["Lip-lip", "action towards White Fang", "fighting"], ["White Fang", "result of <PERSON>p<PERSON><PERSON>'s actions", "forced to leave"]], [["Beauty", "used", "alcohol"], ["Beauty", "con", "Grey Beaver"], ["Beauty", "visited", "<PERSON> Beaver's camp"], ["Beauty", "brought", "bottles of whisky"], ["Grey Beaver", "had", "powerful thirst"], ["Grey Beaver", "craved", "alcohol"], ["Grey Beaver", "spent", "all his money and goods"], ["Grey Beaver", "on", "whisky"], ["Grey Beaver", "had", "nothing but overwhelming desire for more"], ["<PERSON>", "offered", "buy White Fang"], ["<PERSON>", "offered", "bottles of whisky instead of money"], ["Grey Beaver", "agreed to", "deal"], ["Deal", "involved", "buying White Fang"], ["Deal", "involved", "bottles of whisky instead of money"]]]}